import { createRouter, createWebHistory } from 'vue-router'

// 布局组件
import MainLayout from '../components/layout/MainLayout.vue'

// 页面组件
import Dashboard from '../views/Dashboard.vue'
import Login from '../views/Login.vue'
import Chats from '../views/Chats.vue'
import Search from '../views/Search.vue'
import Analytics from '../views/Analytics.vue'
import Settings from '../views/Settings.vue'
import KeywordAlerts from '../views/KeywordAlerts.vue'

const routes = [
  // 认证路由
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: {
      layout: 'auth',
      requiresAuth: false,
      title: 'Login - TeleSeeker'
    }
  },

  // 主应用路由 - 直接使用页面组件，布局在App.vue中处理
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: Dashboard,
    meta: {
      title: 'Dashboard - TeleSeeker',
      icon: 'dashboard',
      requiresAuth: true
    }
  },
      {
        path: 'chats',
        name: 'chats',
        component: Chats,
        meta: {
          title: 'Chats - TeleSeeker',
          icon: 'chat'
        }
      },
      {
        path: 'chats/:chatId',
        name: 'chat-detail',
        component: () => import('../views/ChatDetail.vue'),
        meta: {
          title: 'Chat Detail - TeleSeeker',
          parent: 'chats'
        }
      },
      {
        path: 'search',
        name: 'search',
        component: Search,
        meta: {
          title: 'Search - TeleSeeker',
          icon: 'search'
        }
      },
      {
        path: 'analytics',
        name: 'analytics',
        component: Analytics,
        meta: {
          title: 'Analytics - TeleSeeker',
          icon: 'analytics'
        }
      },
      {
        path: 'alerts',
        name: 'alerts',
        component: KeywordAlerts,
        meta: {
          title: 'Keyword Alerts - TeleSeeker',
          icon: 'bell'
        }
      },
      {
        path: 'settings',
        name: 'settings',
        component: Settings,
        meta: {
          title: 'Settings - TeleSeeker',
          icon: 'settings'
        }
      }
    ]
  },

  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '404 - Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 简化的路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }

  next()
})

export default router
