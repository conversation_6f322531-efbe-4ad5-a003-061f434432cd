<template>
  <div id="app">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载...</p>
    </div>

    <!-- 应用内容 -->
    <template v-else>
      <!-- 认证布局：用于登录页面 -->
      <template v-if="$route.meta?.layout === 'auth' || !isAuthenticated">
        <router-view />
      </template>

      <!-- 应用布局：用于主应用页面 -->
      <template v-else>
        <router-view />
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth-new'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 应用加载状态
const isAppLoading = ref(true)

// 使用auth store的认证状态
const isAuthenticated = computed(() => authStore.isAuthenticated)
const isAuthLoading = computed(() => authStore.isLoading)

// 总的加载状态
const isLoading = computed(() => isAppLoading.value || isAuthLoading.value)

// 应用启动时检查认证状态
onMounted(async () => {
  console.log('🚀 TeleSeeker App 启动，当前路径:', route.path)
  console.log('🔍 开始检查本地session状态...')

  try {
    // 使用auth store的initializeAuth方法检查认证状态
    const hasAuth = await authStore.initializeAuth()

    console.log('📊 认证检查结果:', hasAuth)

    if (hasAuth) {
      console.log('✅ 发现有效session，用户已登录')
      console.log('👤 当前用户:', authStore.currentUser)

      // 有认证，确保跳转到主页
      if (route.path === '/login' || route.path === '/') {
        console.log('🔄 从登录页跳转到主页')
        await router.replace('/dashboard')
      } else {
        console.log('📍 保持当前页面:', route.path)
      }
    } else {
      console.log('❌ 未发现有效session，需要重新登录')

      // 无认证，跳转到登录页
      if (route.path !== '/login') {
        console.log('🔄 跳转到登录页')
        await router.replace('/login')
      } else {
        console.log('📍 已在登录页面')
      }
    }
  } catch (error) {
    console.error('❌ 认证检查失败:', error)
    // 出错时跳转到登录页
    if (route.path !== '/login') {
      await router.replace('/login')
    }
  } finally {
    isAppLoading.value = false
    console.log('🏁 应用初始化完成')
  }
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #6b7280;
  font-size: 14px;
}
</style>