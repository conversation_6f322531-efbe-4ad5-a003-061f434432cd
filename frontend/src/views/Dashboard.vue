<template>
  <div class="dashboard">
    <!-- 数据卡片行 -->
    <div class="stats-row">
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon blue">
            <i class="icon-messages"></i>
          </div>
          <div class="stat-actions">
            <button class="action-btn">⋯</button>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-title">TOTAL MESSAGES</h3>
          <div class="stat-value">{{ formatNumber(totalMessages) }}</div>
          <div class="stat-change positive">
            <span class="change-icon">↗</span>
            <span>+{{ messageGrowth }}%</span>
            <span class="change-period">from last month</span>
          </div>
        </div>
        <div class="stat-chart">
          <svg viewBox="0 0 100 30" class="mini-chart blue">
            <polyline 
              :points="messageChartPoints" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="2"
            />
          </svg>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon purple">
            <i class="icon-chats"></i>
          </div>
          <div class="stat-actions">
            <button class="action-btn">⋯</button>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-title">ACTIVE CHATS</h3>
          <div class="stat-value">{{ formatNumber(activeChats) }}</div>
          <div class="stat-change negative">
            <span class="change-icon">↘</span>
            <span>-{{ chatDecline }}%</span>
            <span class="change-period">from last month</span>
          </div>
        </div>
        <div class="stat-chart">
          <svg viewBox="0 0 100 30" class="mini-chart purple">
            <polyline 
              :points="chatChartPoints" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="2"
            />
          </svg>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-icon green">
            <i class="icon-ai"></i>
          </div>
          <div class="stat-actions">
            <button class="action-btn">⋯</button>
          </div>
        </div>
        <div class="stat-content">
          <h3 class="stat-title">AI SUMMARIES</h3>
          <div class="stat-value">{{ formatNumber(aiSummaries) }}</div>
          <div class="stat-change positive">
            <span class="change-icon">↗</span>
            <span>+{{ summaryGrowth }}%</span>
            <span class="change-period">from last month</span>
          </div>
        </div>
        <div class="stat-chart">
          <svg viewBox="0 0 100 30" class="mini-chart green">
            <polyline 
              :points="summaryChartPoints" 
              fill="none" 
              stroke="currentColor" 
              stroke-width="2"
            />
          </svg>
        </div>
      </div>
    </div>

    <!-- 图表行 -->
    <div class="charts-row">
      <!-- 消息趋势图表 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>Message Trends</h3>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-dot blue"></span>
              <span>Received {{ formatNumber(totalReceived) }}</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot orange"></span>
              <span>Sent {{ formatNumber(totalSent) }}</span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <div class="chart-stats">
            <div class="chart-stat">
              <span class="stat-label">Total</span>
              <span class="stat-number">{{ formatNumber(totalReceived + totalSent) }}</span>
            </div>
            <div class="chart-change">
              <span class="change-text">{{ messageGrowth > 0 ? '+' : '' }}{{ messageGrowth }}%</span>
            </div>
          </div>
          <div class="bar-chart">
            <div v-for="(bar, index) in messageBarData" :key="index" class="bar-group">
              <div class="bar received" :style="{ height: bar.received + '%' }"></div>
              <div class="bar sent" :style="{ height: bar.sent + '%' }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>Real Time Activity</h3>
          <div class="live-indicator">
            <span class="live-dot"></span>
            <span>Live</span>
          </div>
        </div>
        <div class="chart-content">
          <div class="realtime-value">
            <span class="value">{{ formatNumber(realTimeValue) }}</span>
            <div class="value-change positive">
              <span>+{{ realTimeChange }}%</span>
              <span class="change-period">vs last hour</span>
            </div>
          </div>
          <div class="realtime-chart">
            <svg viewBox="0 0 300 100" class="line-chart">
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                  <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0" />
                </linearGradient>
              </defs>
              <path 
                :d="realTimeChartPath" 
                fill="url(#gradient)" 
                stroke="#3b82f6" 
                stroke-width="2"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <div class="activity-header">
        <h3>Recent Activity</h3>
        <button class="view-all-btn">View All</button>
      </div>
      <div class="activity-list">
        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
          <div class="activity-icon" :class="activity.type">
            <i :class="activity.icon"></i>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
          <div class="activity-status" :class="activity.status">
            {{ activity.statusText }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 真实数据 - 从后端API获取
const totalMessages = ref(0)
const messageGrowth = ref(0)
const activeChats = ref(0)
const chatDecline = ref(0)
const aiSummaries = ref(0)
const summaryGrowth = ref(0)
const realTimeValue = ref(0)
const realTimeChange = ref(0)

// 真实图表数据 - 从后端API获取
const messageBarData = ref([])
const recentActivities = ref([])

// 计算属性 - 基于真实数据
const totalReceived = computed(() => totalMessages.value * 0.75) // 假设75%是接收的
const totalSent = computed(() => totalMessages.value * 0.25) // 假设25%是发送的

// 计算属性
const messageChartPoints = computed(() => {
  const points = [10, 15, 12, 18, 14, 20, 16, 22, 18, 25]
  return points.map((y, x) => `${x * 11},${30 - y}`).join(' ')
})

const chatChartPoints = computed(() => {
  const points = [20, 18, 22, 16, 19, 15, 17, 14, 16, 12]
  return points.map((y, x) => `${x * 11},${30 - y}`).join(' ')
})

const summaryChartPoints = computed(() => {
  const points = [8, 12, 10, 16, 14, 18, 16, 20, 18, 22]
  return points.map((y, x) => `${x * 11},${30 - y}`).join(' ')
})

const realTimeChartPath = computed(() => {
  const points = [
    [0, 80], [30, 75], [60, 85], [90, 70], [120, 90], [150, 65], 
    [180, 95], [210, 60], [240, 85], [270, 55], [300, 75]
  ]
  
  let path = `M ${points[0][0]} ${points[0][1]}`
  for (let i = 1; i < points.length; i++) {
    path += ` L ${points[i][0]} ${points[i][1]}`
  }
  path += ` L 300 100 L 0 100 Z`
  
  return path
})

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (time: Date): string => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`
  
  const days = Math.floor(hours / 24)
  return `${days}d ago`
}

// 生命周期
onMounted(async () => {
  console.log('📊 Dashboard 组件加载，开始获取真实数据...')

  // TODO: 从后端API获取真实数据
  // await loadDashboardData()

  console.log('📊 Dashboard 数据加载完成')
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

/* 统计卡片行 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.blue { background: #dbeafe; color: #3b82f6; }
.stat-icon.purple { background: #ede9fe; color: #8b5cf6; }
.stat-icon.green { background: #dcfce7; color: #22c55e; }

.action-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.action-btn:hover {
  background: #f3f4f6;
}

.stat-title {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.stat-change.positive {
  color: #22c55e;
}

.stat-change.negative {
  color: #ef4444;
}

.change-period {
  color: #6b7280;
}

.stat-chart {
  margin-top: 16px;
  height: 30px;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.mini-chart.blue { color: #3b82f6; }
.mini-chart.purple { color: #8b5cf6; }
.mini-chart.green { color: #22c55e; }

/* 图表行 */
.charts-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.blue { background: #3b82f6; }
.legend-dot.orange { background: #f59e0b; }

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #22c55e;
  font-weight: 500;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.chart-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-stat {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.chart-change {
  font-size: 14px;
  color: #6b7280;
}

.bar-chart {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 120px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.bar {
  width: 100%;
  min-height: 4px;
  border-radius: 2px;
}

.bar.received { background: #3b82f6; }
.bar.sent { background: #f59e0b; }

.realtime-value {
  text-align: center;
  margin-bottom: 20px;
}

.realtime-value .value {
  font-size: 36px;
  font-weight: 700;
  color: #111827;
  display: block;
}

.value-change {
  margin-top: 8px;
  font-size: 14px;
}

.value-change.positive {
  color: #22c55e;
}

.realtime-chart {
  height: 100px;
}

.line-chart {
  width: 100%;
  height: 100%;
}

/* 活动部分 */
.activity-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.activity-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.view-all-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-all-btn:hover {
  background: #2563eb;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  transition: border-color 0.2s;
}

.activity-item:hover {
  border-color: #e5e7eb;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.activity-icon.sync { background: #dbeafe; color: #3b82f6; }
.activity-icon.alert { background: #fef3c7; color: #f59e0b; }
.activity-icon.ai { background: #dcfce7; color: #22c55e; }
.activity-icon.search { background: #ede9fe; color: #8b5cf6; }

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
}

.activity-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.activity-status.success {
  background: #dcfce7;
  color: #22c55e;
}

.activity-status.warning {
  background: #fef3c7;
  color: #f59e0b;
}

.activity-status.info {
  background: #dbeafe;
  color: #3b82f6;
}

/* 图标 */
.icon-messages::before { content: "💬"; }
.icon-chats::before { content: "👥"; }
.icon-ai::before { content: "🤖"; }
.icon-sync::before { content: "🔄"; }
.icon-alert::before { content: "⚠️"; }
.icon-search::before { content: "🔍"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .charts-row {
    grid-template-columns: 1fr;
  }
  
  .chart-legend {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
