<template>
  <div class="login-container">
    <div class="login-card">
      <!-- Logo和标题 -->
      <div class="login-header">
        <div class="logo">
          <h1>TeleSeeker</h1>
          <p>商业化第三方Telegram客户端</p>
        </div>
      </div>

      <!-- 登录表单 -->
      <div class="login-form">
        <!-- 登录方式选择 -->
        <div class="login-tabs">
          <button
            class="tab-btn"
            :class="{ active: loginMethod === 'simple' }"
            @click="switchLoginMethod('simple')"
          >
            快速登录
          </button>
          <button
            class="tab-btn"
            :class="{ active: loginMethod === 'advanced' }"
            @click="switchLoginMethod('advanced')"
          >
            高级登录
          </button>
        </div>

        <!-- 快速登录 -->
        <div v-if="loginMethod === 'simple'" class="form-content">
          <!-- 步骤1: 输入手机号 -->
          <div v-if="authStep === 'phone'" class="step-content">
            <h3>输入手机号</h3>
            <p class="step-description">请输入您的Telegram手机号码</p>
            
            <div class="form-group">
              <label>手机号码</label>
              <div class="phone-input-group">
                <select
                  v-model="countryCode"
                  class="country-select"
                  :disabled="isLoading"
                >
                  <option v-for="country in countries" :key="country.code" :value="country.code">
                    {{ country.flag }} {{ country.code }} {{ country.name }}
                  </option>
                </select>
                <input
                  v-model="phoneNumber"
                  type="tel"
                  placeholder="138 0013 8000"
                  class="phone-input"
                  :disabled="isLoading"
                  @keyup.enter="handleRequestCode"
                />
              </div>
              <p class="input-hint">完整号码: {{ countryCode }} {{ phoneNumber }}</p>
            </div>

            <button 
              class="btn-primary full-width"
              @click="handleRequestCode"
              :disabled="isLoading || !phoneNumber.trim()"
            >
              <span v-if="isLoading">发送中...</span>
              <span v-else>发送验证码</span>
            </button>
          </div>

          <!-- 步骤2: 输入验证码 -->
          <div v-if="authStep === 'code'" class="step-content">
            <h3>输入验证码</h3>
            <p class="step-description">
              验证码已发送到 {{ phoneNumber }}
            </p>
            
            <div class="form-group">
              <label>验证码</label>
              <input
                v-model="verificationCode"
                type="text"
                placeholder="请输入6位验证码"
                class="form-input"
                :disabled="isLoading"
                maxlength="6"
                @keyup.enter="handleVerifyCode"
              />
            </div>

            <div class="form-actions">
              <button 
                class="btn-secondary"
                @click="goBack"
                :disabled="isLoading"
              >
                返回
              </button>
              <button 
                class="btn-primary"
                @click="handleVerifyCode"
                :disabled="isLoading || !verificationCode.trim()"
              >
                <span v-if="isLoading">验证中...</span>
                <span v-else>验证</span>
              </button>
            </div>
          </div>

          <!-- 步骤3: 输入密码（两步验证） -->
          <div v-if="authStep === 'password'" class="step-content">
            <h3>两步验证</h3>
            <p class="step-description">
              您的账户启用了两步验证，请输入密码
            </p>
            
            <div class="form-group">
              <label>密码</label>
              <input
                v-model="password"
                type="password"
                placeholder="请输入两步验证密码"
                class="form-input"
                :disabled="isLoading"
                @keyup.enter="handleVerifyPassword"
              />
            </div>

            <div class="form-actions">
              <button 
                class="btn-secondary"
                @click="goBack"
                :disabled="isLoading"
              >
                返回
              </button>
              <button 
                class="btn-primary"
                @click="handleVerifyPassword"
                :disabled="isLoading || !password.trim()"
              >
                <span v-if="isLoading">验证中...</span>
                <span v-else>验证</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 高级登录 -->
        <div v-if="loginMethod === 'advanced'" class="form-content">
          <!-- 步骤1: 配置API -->
          <div v-if="authStep === 'phone'" class="step-content">
            <h3>配置API凭据</h3>
            <p class="step-description">
              请先配置您的Telegram API凭据，然后输入手机号码
              <a href="https://my.telegram.org/apps" target="_blank" class="help-link">
                获取API凭据
              </a>
            </p>

            <div class="form-group">
              <label>API ID</label>
              <input
                v-model.number="apiId"
                type="number"
                placeholder="从 my.telegram.org 获取"
                class="form-input"
                :disabled="isLoading"
              />
            </div>

            <div class="form-group">
              <label>API Hash</label>
              <input
                v-model="apiHash"
                type="password"
                placeholder="从 my.telegram.org 获取"
                class="form-input"
                :disabled="isLoading"
              />
            </div>

            <div class="form-group">
              <label>手机号码</label>
              <div class="phone-input-group">
                <select
                  v-model="countryCode"
                  class="country-select"
                  :disabled="isLoading"
                >
                  <option v-for="country in countries" :key="country.code" :value="country.code">
                    {{ country.flag }} {{ country.code }} {{ country.name }}
                  </option>
                </select>
                <input
                  v-model="phoneNumber"
                  type="tel"
                  placeholder="138 0013 8000"
                  class="phone-input"
                  :disabled="isLoading"
                  @keyup.enter="handleAdvancedRequestCode"
                />
              </div>
              <p class="input-hint">完整号码: {{ countryCode }} {{ phoneNumber }}</p>
            </div>

            <button
              class="btn-primary full-width"
              @click="handleAdvancedRequestCode"
              :disabled="isLoading || !apiId || !apiHash.trim() || !phoneNumber.trim()"
            >
              <span v-if="isLoading">发送中...</span>
              <span v-else>发送验证码</span>
            </button>
          </div>

          <!-- 步骤2: 输入验证码 -->
          <div v-if="authStep === 'code'" class="step-content">
            <h3>输入验证码</h3>
            <p class="step-description">
              验证码已发送到 {{ countryCode }} {{ phoneNumber }}
            </p>

            <div class="form-group">
              <label>验证码</label>
              <input
                v-model="verificationCode"
                type="text"
                placeholder="请输入6位验证码"
                class="form-input"
                :disabled="isLoading"
                maxlength="6"
                @keyup.enter="handleAdvancedVerifyCode"
              />
            </div>

            <div class="form-actions">
              <button
                class="btn-secondary"
                @click="goBack"
                :disabled="isLoading"
              >
                返回
              </button>
              <button
                class="btn-primary"
                @click="handleAdvancedVerifyCode"
                :disabled="isLoading || !verificationCode.trim()"
              >
                <span v-if="isLoading">验证中...</span>
                <span v-else>验证</span>
              </button>
            </div>
          </div>

          <!-- 步骤3: 输入密码（两步验证） -->
          <div v-if="authStep === 'password'" class="step-content">
            <h3>两步验证</h3>
            <p class="step-description">
              您的账户启用了两步验证，请输入密码
            </p>

            <div class="form-group">
              <label>密码</label>
              <input
                v-model="password"
                type="password"
                placeholder="请输入两步验证密码"
                class="form-input"
                :disabled="isLoading"
                @keyup.enter="handleAdvancedVerifyPassword"
              />
            </div>

            <div class="form-actions">
              <button
                class="btn-secondary"
                @click="goBack"
                :disabled="isLoading"
              >
                返回
              </button>
              <button
                class="btn-primary"
                @click="handleAdvancedVerifyPassword"
                :disabled="isLoading || !password.trim()"
              >
                <span v-if="isLoading">验证中...</span>
                <span v-else>验证</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="error" class="error-message">
          {{ error }}
        </div>

        <!-- 成功信息 -->
        <div v-if="authStep === 'success'" class="success-message">
          <div class="success-icon">✓</div>
          <h3>登录成功</h3>
          <p>正在跳转到主页面...</p>
        </div>
      </div>

      <!-- 页脚 -->
      <div class="login-footer">
        <p>
          使用TeleSeeker即表示您同意我们的
          <a href="/terms" target="_blank">服务条款</a>
          和
          <a href="/privacy" target="_blank">隐私政策</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth-new'

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loginMethod = ref<'simple' | 'advanced'>('simple')
const countryCode = ref('+86')
const phoneNumber = ref('')
const verificationCode = ref('')
const password = ref('')
const apiId = ref<number | null>(null)
const apiHash = ref('')

// 国家代码列表
const countries = ref([
  { code: '+86', name: '中国', flag: '🇨🇳' },
  { code: '+1', name: '美国', flag: '🇺🇸' },
  { code: '+44', name: '英国', flag: '🇬🇧' },
  { code: '+81', name: '日本', flag: '🇯🇵' },
  { code: '+82', name: '韩国', flag: '🇰🇷' },
  { code: '+65', name: '新加坡', flag: '🇸🇬' },
  { code: '+852', name: '香港', flag: '🇭🇰' },
  { code: '+853', name: '澳门', flag: '🇲🇴' },
  { code: '+886', name: '台湾', flag: '🇹🇼' },
  { code: '+7', name: '俄罗斯', flag: '🇷🇺' },
  { code: '+49', name: '德国', flag: '🇩🇪' },
  { code: '+33', name: '法国', flag: '🇫🇷' },
  { code: '+39', name: '意大利', flag: '🇮🇹' },
  { code: '+34', name: '西班牙', flag: '🇪🇸' },
  { code: '+31', name: '荷兰', flag: '🇳🇱' },
  { code: '+46', name: '瑞典', flag: '🇸🇪' },
  { code: '+47', name: '挪威', flag: '🇳🇴' },
  { code: '+45', name: '丹麦', flag: '🇩🇰' },
  { code: '+358', name: '芬兰', flag: '🇫🇮' },
  { code: '+41', name: '瑞士', flag: '🇨🇭' },
  { code: '+43', name: '奥地利', flag: '🇦🇹' },
  { code: '+32', name: '比利时', flag: '🇧🇪' },
  { code: '+351', name: '葡萄牙', flag: '🇵🇹' },
  { code: '+30', name: '希腊', flag: '🇬🇷' },
  { code: '+90', name: '土耳其', flag: '🇹🇷' },
  { code: '+91', name: '印度', flag: '🇮🇳' },
  { code: '+60', name: '马来西亚', flag: '🇲🇾' },
  { code: '+66', name: '泰国', flag: '🇹🇭' },
  { code: '+84', name: '越南', flag: '🇻🇳' },
  { code: '+63', name: '菲律宾', flag: '🇵🇭' },
  { code: '+62', name: '印尼', flag: '🇮🇩' },
  { code: '+61', name: '澳大利亚', flag: '🇦🇺' },
  { code: '+64', name: '新西兰', flag: '🇳🇿' },
  { code: '+55', name: '巴西', flag: '🇧🇷' },
  { code: '+52', name: '墨西哥', flag: '🇲🇽' },
  { code: '+54', name: '阿根廷', flag: '🇦🇷' },
  { code: '+56', name: '智利', flag: '🇨🇱' },
  { code: '+57', name: '哥伦比亚', flag: '🇨🇴' },
  { code: '+51', name: '秘鲁', flag: '🇵🇪' },
  { code: '+58', name: '委内瑞拉', flag: '🇻🇪' },
  { code: '+27', name: '南非', flag: '🇿🇦' },
  { code: '+20', name: '埃及', flag: '🇪🇬' },
  { code: '+234', name: '尼日利亚', flag: '🇳🇬' },
  { code: '+254', name: '肯尼亚', flag: '🇰🇪' },
  { code: '+212', name: '摩洛哥', flag: '🇲🇦' },
  { code: '+213', name: '阿尔及利亚', flag: '🇩🇿' },
  { code: '+216', name: '突尼斯', flag: '🇹🇳' },
  { code: '+218', name: '利比亚', flag: '🇱🇾' },
  { code: '+98', name: '伊朗', flag: '🇮🇷' },
  { code: '+964', name: '伊拉克', flag: '🇮🇶' },
  { code: '+966', name: '沙特', flag: '🇸🇦' },
  { code: '+971', name: '阿联酋', flag: '🇦🇪' },
  { code: '+974', name: '卡塔尔', flag: '🇶🇦' },
  { code: '+965', name: '科威特', flag: '🇰🇼' },
  { code: '+973', name: '巴林', flag: '🇧🇭' },
  { code: '+968', name: '阿曼', flag: '🇴🇲' },
  { code: '+962', name: '约旦', flag: '🇯🇴' },
  { code: '+961', name: '黎巴嫩', flag: '🇱🇧' },
  { code: '+963', name: '叙利亚', flag: '🇸🇾' },
  { code: '+972', name: '以色列', flag: '🇮🇱' },
  { code: '+970', name: '巴勒斯坦', flag: '🇵🇸' }
])

// 计算属性
const authStep = computed(() => authStore.authStep)
const isLoading = computed(() => authStore.isLoading)
const error = computed(() => authStore.error)
const requiresPassword = computed(() => authStore.requiresPassword)

// 方法
const handleRequestCode = async () => {
  try {
    authStore.clearError()
    const fullPhoneNumber = countryCode.value + phoneNumber.value
    await authStore.requestCode(fullPhoneNumber)
  } catch (err) {
    console.error('请求验证码失败:', err)
  }
}

const handleAdvancedRequestCode = async () => {
  try {
    authStore.clearError()
    const fullPhoneNumber = countryCode.value + phoneNumber.value
    await authStore.requestCodeWithApi(fullPhoneNumber, apiId.value!, apiHash.value)
  } catch (err) {
    console.error('高级登录请求验证码失败:', err)
  }
}

const handleVerifyCode = async () => {
  try {
    authStore.clearError()
    const fullPhoneNumber = countryCode.value + phoneNumber.value
    await authStore.loginWithPhone(fullPhoneNumber, verificationCode.value)

    if (!requiresPassword.value) {
      // 登录成功，跳转到主页
      setTimeout(() => {
        router.push('/dashboard')
      }, 1000)
    }
  } catch (err) {
    console.error('验证码验证失败:', err)
  }
}

const handleAdvancedVerifyCode = async () => {
  try {
    authStore.clearError()
    const fullPhoneNumber = countryCode.value + phoneNumber.value
    await authStore.loginWithPhoneAndApi(fullPhoneNumber, verificationCode.value, apiId.value!, apiHash.value)

    if (!requiresPassword.value) {
      // 登录成功，跳转到主页
      setTimeout(() => {
        router.push('/dashboard')
      }, 1000)
    }
  } catch (err) {
    console.error('高级验证码验证失败:', err)
  }
}

const handleVerifyPassword = async () => {
  try {
    authStore.clearError()
    await authStore.loginWithPassword(password.value)

    // 登录成功，跳转到主页
    setTimeout(() => {
      router.push('/dashboard')
    }, 1000)
  } catch (err) {
    console.error('密码验证失败:', err)
  }
}

const handleAdvancedVerifyPassword = async () => {
  try {
    authStore.clearError()
    await authStore.loginWithPasswordAndApi(password.value, apiId.value!, apiHash.value)

    // 登录成功，跳转到主页
    setTimeout(() => {
      router.push('/dashboard')
    }, 1000)
  } catch (err) {
    console.error('高级密码验证失败:', err)
  }
}

const goBack = () => {
  authStore.resetAuthFlow()
  verificationCode.value = ''
  password.value = ''
}

// 切换登录方式时重置状态
const switchLoginMethod = (method: 'simple' | 'advanced') => {
  loginMethod.value = method
  authStore.resetAuthFlow()
  authStore.clearError()

  // 清空表单
  countryCode.value = '+86'
  phoneNumber.value = ''
  verificationCode.value = ''
  password.value = ''
  apiId.value = null
  apiHash.value = ''
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  overflow: hidden;
}

.login-header {
  text-align: center;
  padding: 40px 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.logo h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px;
}

.logo p {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.login-form {
  padding: 30px 40px;
}

.login-tabs {
  display: flex;
  margin-bottom: 30px;
  border-radius: 8px;
  background: #f8f9fa;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
}

.tab-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px;
}

.step-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px;
  line-height: 1.5;
}

.help-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.help-link:hover {
  text-decoration: underline;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.phone-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.country-select {
  min-width: 120px;
  padding: 12px 8px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.country-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.country-select:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.phone-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.phone-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.phone-input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.input-hint {
  font-size: 12px;
  color: #6b7280;
  margin: 4px 0 0;
  font-style: italic;
}

.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  gap: 12px;
}

.form-actions .btn-primary,
.form-actions .btn-secondary {
  flex: 1;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-top: 16px;
}

.success-message {
  text-align: center;
  padding: 20px;
}

.success-icon {
  width: 60px;
  height: 60px;
  background: #22c55e;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin: 0 auto 16px;
}

.success-message h3 {
  color: #22c55e;
  margin: 0 0 8px;
}

.success-message p {
  color: #6b7280;
  margin: 0;
}

.login-footer {
  padding: 20px 40px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.login-footer p {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.login-footer a {
  color: #3b82f6;
  text-decoration: none;
}

.login-footer a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-header,
  .login-form,
  .login-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .phone-input-group {
    flex-direction: column;
    gap: 12px;
  }

  .country-select {
    width: 100%;
    min-width: auto;
  }

  .phone-input {
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions .btn-primary,
  .form-actions .btn-secondary {
    width: 100%;
  }
}
</style>
