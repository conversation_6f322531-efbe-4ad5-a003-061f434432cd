<template>
  <div class="search-page">
    <div class="search-container">
      <!-- 搜索框 -->
      <div class="search-box">
        <div class="search-input-wrapper">
          <i class="icon-search"></i>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入关键词搜索消息..."
            @keyup.enter="performSearch"
            class="search-input"
          />
          <button @click="performSearch" class="search-btn" :disabled="!searchQuery.trim()">
            搜索
          </button>
        </div>
      </div>

      <!-- 搜索选项 -->
      <div class="search-options">
        <div class="option-group">
          <label>搜索类型:</label>
          <div class="radio-group">
            <label class="radio-item">
              <input type="radio" v-model="searchType" value="keyword" />
              <span>关键词搜索</span>
            </label>
            <label class="radio-item">
              <input type="radio" v-model="searchType" value="semantic" />
              <span>语义搜索</span>
            </label>
          </div>
        </div>

        <div class="option-group">
          <label>时间范围:</label>
          <select v-model="timeRange" class="time-select">
            <option value="all">全部时间</option>
            <option value="today">今天</option>
            <option value="week">最近一周</option>
            <option value="month">最近一月</option>
            <option value="year">最近一年</option>
          </select>
        </div>

        <div class="option-group">
          <label>聊天范围:</label>
          <select v-model="chatFilter" class="chat-select">
            <option value="all">所有聊天</option>
            <option value="groups">仅群组</option>
            <option value="channels">仅频道</option>
            <option value="private">仅私聊</option>
          </select>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <div v-if="isSearching" class="loading-state">
          <div class="loading-spinner"></div>
          <p>正在搜索...</p>
        </div>

        <div v-else-if="searchResults.length > 0" class="results-list">
          <div class="results-header">
            <h3>搜索结果 ({{ searchResults.length }})</h3>
          </div>
          
          <div v-for="result in searchResults" :key="result.id" class="result-item">
            <div class="result-header">
              <div class="chat-info">
                <span class="chat-name">{{ result.chatName }}</span>
                <span class="chat-type">{{ result.chatType }}</span>
              </div>
              <div class="message-time">{{ formatTime(result.timestamp) }}</div>
            </div>
            
            <div class="message-content" v-html="highlightText(result.content)"></div>
            
            <div class="result-actions">
              <button @click="goToMessage(result)" class="action-btn">
                <i class="icon-link"></i>
                跳转到消息
              </button>
              <button @click="copyMessage(result)" class="action-btn">
                <i class="icon-copy"></i>
                复制
              </button>
            </div>
          </div>
        </div>

        <div v-else-if="hasSearched && searchResults.length === 0" class="empty-results">
          <div class="empty-icon">🔍</div>
          <h3>未找到相关结果</h3>
          <p>尝试使用不同的关键词或调整搜索选项</p>
        </div>

        <div v-else class="search-placeholder">
          <div class="placeholder-icon">💬</div>
          <h3>开始搜索</h3>
          <p>输入关键词来搜索您的聊天记录</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const searchQuery = ref('')
const searchType = ref('keyword')
const timeRange = ref('all')
const chatFilter = ref('all')
const isSearching = ref(false)
const hasSearched = ref(false)
const searchResults = ref<any[]>([])

// 真实搜索结果 - 从后端API获取

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  console.log('🔍 开始搜索:', searchQuery.value)
  console.log('🔍 搜索类型:', searchType.value)
  console.log('🔍 时间范围:', timeRange.value)
  console.log('🔍 聊天范围:', chatFilter.value)

  isSearching.value = true
  hasSearched.value = true

  try {
    // TODO: 调用真实的搜索API
    // const response = await apiClient.post('/search', {
    //   query: searchQuery.value,
    //   type: searchType.value,
    //   timeRange: timeRange.value,
    //   chatFilter: chatFilter.value
    // })
    // searchResults.value = response.data.results

    // 暂时返回空结果，等待真实API实现
    await new Promise(resolve => setTimeout(resolve, 500))
    searchResults.value = []

    console.log('🔍 搜索完成，结果数量:', searchResults.value.length)
  } catch (error) {
    console.error('❌ 搜索失败:', error)
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}

// 高亮搜索关键词
const highlightText = (text: string) => {
  if (!searchQuery.value.trim()) return text
  
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString()
}

// 跳转到消息
const goToMessage = (result: any) => {
  console.log('跳转到消息:', result)
  // 这里应该实现跳转到具体消息的逻辑
}

// 复制消息
const copyMessage = (result: any) => {
  navigator.clipboard.writeText(result.content)
  console.log('已复制消息内容')
}
</script>

<style scoped>
.search-page {
  max-width: 1000px;
  margin: 0 auto;
}

.search-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 24px;
}

.search-input-wrapper i {
  position: absolute;
  left: 16px;
  color: #9ca3af;
  font-size: 18px;
}

.search-input {
  flex: 1;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
  margin-left: 12px;
  padding: 16px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-btn:hover:not(:disabled) {
  background: #2563eb;
}

.search-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.search-options {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  flex-wrap: wrap;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.time-select,
.chat-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.loading-state,
.empty-results,
.search-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-header {
  margin-bottom: 20px;
}

.results-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.result-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  transition: box-shadow 0.2s;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-name {
  font-weight: 600;
  color: #111827;
}

.chat-type {
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.message-time {
  color: #9ca3af;
  font-size: 14px;
}

.message-content {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 16px;
}

.message-content :deep(mark) {
  background: #fef3c7;
  color: #92400e;
  padding: 2px 4px;
  border-radius: 3px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f3f4f6;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
}

.placeholder-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 图标 */
.icon-search::before { content: "🔍"; }
.icon-link::before { content: "🔗"; }
.icon-copy::before { content: "📋"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .search-options {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-input-wrapper {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-btn {
    margin-left: 0;
    width: 100%;
  }
}
</style>
