<template>
  <div class="chats-page">
    <!-- 操作栏 -->
    <div class="actions-bar">
      <button class="btn-primary" @click="syncChats">
        <i class="icon-sync"></i>
        Sync Chats
      </button>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filters-section">
      <div class="search-box">
        <i class="icon-search"></i>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search chats..."
          @input="handleSearch"
        />
      </div>
      
      <div class="filter-buttons">
        <button 
          class="filter-btn"
          :class="{ active: activeFilter === 'all' }"
          @click="setFilter('all')"
        >
          All ({{ totalChats }})
        </button>
        <button 
          class="filter-btn"
          :class="{ active: activeFilter === 'groups' }"
          @click="setFilter('groups')"
        >
          Groups ({{ groupCount }})
        </button>
        <button 
          class="filter-btn"
          :class="{ active: activeFilter === 'channels' }"
          @click="setFilter('channels')"
        >
          Channels ({{ channelCount }})
        </button>
        <button 
          class="filter-btn"
          :class="{ active: activeFilter === 'private' }"
          @click="setFilter('private')"
        >
          Private ({{ privateCount }})
        </button>
      </div>
    </div>

    <!-- 聊天列表 -->
    <div class="chats-grid">
      <div
        v-for="chat in filteredChats"
        :key="chat.telegram_chat_id"
        class="chat-card"
        @click="openChat(chat)"
      >
        <div class="chat-avatar">
          <div class="avatar-placeholder" :class="getChatTypeClass(chat.type)">
            {{ getChatInitial(chat.title) }}
          </div>
        </div>
        
        <div class="chat-info">
          <div class="chat-title">{{ chat.title }}</div>
          <div class="chat-meta">
            <span class="chat-type">{{ formatChatType(chat.type) }}</span>
            <span class="chat-date">{{ formatDate(chat.updated_at) }}</span>
          </div>
          <div class="chat-stats">
            <span v-if="chat.last_synced_message_id" class="stat">
              Last sync: #{{ chat.last_synced_message_id }}
            </span>
            <span v-if="chat.is_archived" class="archived-badge">Archived</span>
          </div>
        </div>
        
        <div class="chat-actions">
          <button class="action-btn" @click.stop="downloadHistory(chat)">
            <i class="icon-download"></i>
          </button>
          <button class="action-btn" @click.stop="toggleArchive(chat)">
            <i :class="chat.is_archived ? 'icon-unarchive' : 'icon-archive'"></i>
          </button>
          <button class="action-btn" @click.stop="showChatMenu(chat)">
            <i class="icon-more"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredChats.length === 0 && !isLoading" class="empty-state">
      <div class="empty-icon">💬</div>
      <h3>No chats found</h3>
      <p v-if="searchQuery">
        No chats match your search "{{ searchQuery }}"
      </p>
      <p v-else-if="activeFilter !== 'all'">
        No {{ activeFilter }} chats available
      </p>
      <p v-else>
        Start by syncing your Telegram chats
      </p>
      <button v-if="!searchQuery && activeFilter === 'all'" class="btn-primary" @click="syncChats">
        Sync Chats
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Loading chats...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { apiClient } from '../utils/api-new'

// 路由
const router = useRouter()

// 响应式数据
const chats = ref<any[]>([])
const isLoading = ref(false)
const searchQuery = ref('')
const activeFilter = ref('all')

// 计算属性
const filteredChats = computed(() => {
  let filtered = chats.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(chat =>
      chat.title.toLowerCase().includes(query)
    )
  }

  // 类型过滤
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter(chat => {
      switch (activeFilter.value) {
        case 'groups':
          return chat.type === 'group' || chat.type === 'supergroup'
        case 'channels':
          return chat.type === 'channel'
        case 'private':
          return chat.type === 'private'
        default:
          return true
      }
    })
  }

  return filtered
})

const totalChats = computed(() => chats.value.length)
const groupCount = computed(() => 
  chats.value.filter(chat => chat.type === 'group' || chat.type === 'supergroup').length
)
const channelCount = computed(() => 
  chats.value.filter(chat => chat.type === 'channel').length
)
const privateCount = computed(() => 
  chats.value.filter(chat => chat.type === 'private').length
)

// 方法
const loadChats = async () => {
  try {
    isLoading.value = true
    const response = await apiClient.getChats()
    chats.value = response.data || []
  } catch (error) {
    console.error('Failed to load chats:', error)
  } finally {
    isLoading.value = false
  }
}

const syncChats = async () => {
  try {
    console.log('🔄 开始同步Telegram聊天...')
    isLoading.value = true

    // TODO: 调用真实的同步API
    // await apiClient.syncChats()

    // 暂时模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    await loadChats()

    console.log('✅ 聊天同步完成')
  } catch (error) {
    console.error('❌ 同步聊天失败:', error)
  } finally {
    isLoading.value = false
  }
}

const setFilter = (filter: string) => {
  activeFilter.value = filter
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const openChat = (chat: any) => {
  router.push(`/chats/${chat.telegram_chat_id}`)
}

const downloadHistory = async (chat: any) => {
  try {
    console.log('Downloading history for chat:', chat.title)
    // 调用下载历史API
    await apiClient.downloadHistory(chat.telegram_chat_id)
  } catch (error) {
    console.error('Failed to download history:', error)
  }
}

const toggleArchive = async (chat: any) => {
  try {
    // 切换归档状态
    chat.is_archived = !chat.is_archived
    console.log(`${chat.is_archived ? 'Archived' : 'Unarchived'} chat:`, chat.title)
  } catch (error) {
    console.error('Failed to toggle archive:', error)
  }
}

const showChatMenu = (chat: any) => {
  console.log('Show menu for chat:', chat.title)
  // 显示更多操作菜单
}

const getChatTypeClass = (type: string) => {
  switch (type) {
    case 'channel':
      return 'channel'
    case 'group':
    case 'supergroup':
      return 'group'
    case 'private':
      return 'private'
    default:
      return 'default'
  }
}

const getChatInitial = (title: string) => {
  return title.charAt(0).toUpperCase()
}

const formatChatType = (type: string) => {
  switch (type) {
    case 'channel':
      return 'Channel'
    case 'group':
      return 'Group'
    case 'supergroup':
      return 'Supergroup'
    case 'private':
      return 'Private'
    default:
      return type
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return 'Today'
  } else if (days === 1) {
    return 'Yesterday'
  } else if (days < 7) {
    return `${days} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

// 生命周期
onMounted(() => {
  loadChats()
})
</script>

<style scoped>
.chats-page {
  max-width: 1200px;
  margin: 0 auto;
}

.actions-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background: #2563eb;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-box input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.chats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.chat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 16px;
}

.chat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chat-avatar {
  flex-shrink: 0;
}

.avatar-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.avatar-placeholder.channel {
  background: #3b82f6;
}

.avatar-placeholder.group {
  background: #22c55e;
}

.avatar-placeholder.private {
  background: #8b5cf6;
}

.avatar-placeholder.default {
  background: #6b7280;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.chat-type {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.chat-date {
  font-size: 12px;
  color: #9ca3af;
}

.chat-stats {
  font-size: 12px;
  color: #6b7280;
}

.archived-badge {
  background: #fbbf24;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  margin-left: 8px;
}

.chat-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
}

.empty-state,
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #111827;
  margin: 0 0 8px;
}

.empty-state p {
  margin: 0 0 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标 */
.icon-sync::before { content: "🔄"; }
.icon-search::before { content: "🔍"; }
.icon-download::before { content: "⬇️"; }
.icon-archive::before { content: "📦"; }
.icon-unarchive::before { content: "📤"; }
.icon-more::before { content: "⋯"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .chats-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-buttons {
    justify-content: center;
  }
}
</style>
