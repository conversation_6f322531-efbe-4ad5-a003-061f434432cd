<template>
  <div class="chats-page">
    <!-- 左侧聊天列表 -->
    <div class="chats-sidebar">
      <!-- 搜索框 -->
      <div class="sidebar-search">
        <i class="icon-search"></i>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索聊天..."
          @input="handleSearch"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="sidebar-actions">
        <button class="sync-btn" @click="syncChats" :disabled="isLoading">
          <i class="icon-sync" :class="{ spinning: isLoading }"></i>
          {{ isLoading ? '同步中...' : '同步聊天' }}
        </button>
      </div>

      <!-- 分类标签 -->
      <div class="category-tabs">
        <button
          class="category-tab"
          :class="{ active: activeCategory === 'all' }"
          @click="setCategory('all')"
        >
          <i class="icon-all"></i>
          全部 ({{ totalChats }})
        </button>
        <button
          class="category-tab"
          :class="{ active: activeCategory === 'private' }"
          @click="setCategory('private')"
        >
          <i class="icon-user"></i>
          联系人 ({{ privateCount }})
        </button>
        <button
          class="category-tab"
          :class="{ active: activeCategory === 'groups' }"
          @click="setCategory('groups')"
        >
          <i class="icon-group"></i>
          群组 ({{ groupCount }})
        </button>
        <button
          class="category-tab"
          :class="{ active: activeCategory === 'channels' }"
          @click="setCategory('channels')"
        >
          <i class="icon-channel"></i>
          频道 ({{ channelCount }})
        </button>
        <button
          class="category-tab"
          :class="{ active: activeCategory === 'bots' }"
          @click="setCategory('bots')"
        >
          <i class="icon-bot"></i>
          机器人 ({{ botCount }})
        </button>
      </div>

      <!-- 聊天列表 -->
      <div class="chats-list">
        <!-- 置顶聊天 -->
        <div v-if="pinnedChats.length > 0" class="chat-group">
          <div class="group-header">
            <i class="icon-pin"></i>
            <span>置顶聊天</span>
          </div>
          <div
            v-for="chat in pinnedChats"
            :key="chat.id"
            class="chat-item"
            :class="{ active: selectedChat?.id === chat.id }"
            @click="selectChat(chat)"
          >
            <div class="chat-avatar">
              <img
                v-if="chat.avatar_url"
                :src="chat.avatar_url"
                :alt="chat.title"
                @error="handleAvatarError"
              />
              <div v-else class="avatar-placeholder" :class="getChatTypeClass(chat.type)">
                {{ getChatInitial(chat.title) }}
              </div>
              <div v-if="chat.unread_count > 0" class="unread-badge">
                {{ chat.unread_count > 99 ? '99+' : chat.unread_count }}
              </div>
            </div>
            <div class="chat-info">
              <div class="chat-title">{{ chat.title }}</div>
              <div class="chat-meta">
                <span class="chat-type">{{ formatChatType(chat.type) }}</span>
                <span v-if="chat.participants_count" class="member-count">
                  {{ formatMemberCount(chat.participants_count) }}
                </span>
              </div>
            </div>
            <div class="chat-status">
              <i v-if="chat.is_muted" class="icon-mute"></i>
              <span class="last-time">{{ formatLastTime(chat.last_message_date) }}</span>
            </div>
          </div>
        </div>

        <!-- 普通聊天 -->
        <div v-if="regularChats.length > 0" class="chat-group">
          <div class="group-header">
            <i class="icon-chat"></i>
            <span>{{ getCategoryTitle() }}</span>
          </div>
          <div
            v-for="chat in regularChats"
            :key="chat.id"
            class="chat-item"
            :class="{ active: selectedChat?.id === chat.id }"
            @click="selectChat(chat)"
          >
            <div class="chat-avatar">
              <img
                v-if="chat.avatar_url"
                :src="chat.avatar_url"
                :alt="chat.title"
                @error="handleAvatarError"
              />
              <div v-else class="avatar-placeholder" :class="getChatTypeClass(chat.type)">
                {{ getChatInitial(chat.title) }}
              </div>
              <div v-if="chat.unread_count > 0" class="unread-badge">
                {{ chat.unread_count > 99 ? '99+' : chat.unread_count }}
              </div>
            </div>
            <div class="chat-info">
              <div class="chat-title">{{ chat.title }}</div>
              <div class="chat-meta">
                <span class="chat-type">{{ formatChatType(chat.type) }}</span>
                <span v-if="chat.participants_count" class="member-count">
                  {{ formatMemberCount(chat.participants_count) }}
                </span>
                <span v-if="chat.username" class="username">@{{ chat.username }}</span>
              </div>
            </div>
            <div class="chat-status">
              <i v-if="chat.is_muted" class="icon-mute"></i>
              <span class="last-time">{{ formatLastTime(chat.last_message_date) }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredChats.length === 0 && !isLoading" class="empty-state">
          <div class="empty-icon">{{ getEmptyIcon() }}</div>
          <div class="empty-text">
            <h4>{{ getEmptyTitle() }}</h4>
            <p>{{ getEmptyMessage() }}</p>
          </div>
          <button v-if="activeCategory === 'all'" class="sync-btn" @click="syncChats">
            同步聊天
          </button>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>正在加载聊天...</p>
        </div>
      </div>
    </div>

    <!-- 右侧聊天详情 -->
    <div class="chat-detail">
      <!-- 选中聊天的详情 -->
      <div v-if="selectedChat" class="chat-detail-content">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="chat-avatar-large">
            <img
              v-if="selectedChat.avatar_url"
              :src="selectedChat.avatar_url"
              :alt="selectedChat.title"
              @error="handleAvatarError"
            />
            <div v-else class="avatar-placeholder-large" :class="getChatTypeClass(selectedChat.type)">
              {{ getChatInitial(selectedChat.title) }}
            </div>
          </div>
          <div class="chat-header-info">
            <h2 class="chat-title-large">{{ selectedChat.title }}</h2>
            <div class="chat-meta-large">
              <span class="chat-type-badge" :class="getChatTypeClass(selectedChat.type)">
                {{ formatChatType(selectedChat.type) }}
              </span>
              <span v-if="selectedChat.username" class="username-large">@{{ selectedChat.username }}</span>
              <span v-if="selectedChat.participants_count" class="member-count-large">
                {{ formatMemberCount(selectedChat.participants_count) }} 成员
              </span>
            </div>
            <div class="chat-stats-large">
              <div class="stat-item">
                <i class="icon-message"></i>
                <span>未读消息: {{ selectedChat.unread_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <i class="icon-time"></i>
                <span>最后活动: {{ formatLastTime(selectedChat.last_message_date) }}</span>
              </div>
              <div v-if="selectedChat.is_muted" class="stat-item">
                <i class="icon-mute"></i>
                <span>已静音</span>
              </div>
            </div>
          </div>
          <div class="chat-actions-large">
            <button class="action-btn-large" @click="openChatMessages(selectedChat)">
              <i class="icon-message"></i>
              查看消息
            </button>
            <button class="action-btn-large" @click="downloadHistory(selectedChat)">
              <i class="icon-download"></i>
              下载历史
            </button>
            <button class="action-btn-large" @click="toggleMute(selectedChat)">
              <i :class="selectedChat.is_muted ? 'icon-unmute' : 'icon-mute'"></i>
              {{ selectedChat.is_muted ? '取消静音' : '静音' }}
            </button>
          </div>
        </div>

        <!-- 聊天统计 -->
        <div class="chat-statistics">
          <h3>聊天统计</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-value">{{ selectedChat.unread_count || 0 }}</div>
                <div class="stat-label">未读消息</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">👥</div>
              <div class="stat-info">
                <div class="stat-value">{{ selectedChat.participants_count || 'N/A' }}</div>
                <div class="stat-label">成员数量</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📅</div>
              <div class="stat-info">
                <div class="stat-value">{{ formatDate(selectedChat.last_message_date) }}</div>
                <div class="stat-label">最后消息</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">⚙️</div>
              <div class="stat-info">
                <div class="stat-value">{{ selectedChat.is_pinned ? '是' : '否' }}</div>
                <div class="stat-label">置顶状态</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 未选中聊天时的提示 -->
      <div v-else class="no-chat-selected">
        <div class="no-chat-icon">💬</div>
        <h3>选择一个聊天</h3>
        <p>从左侧列表中选择一个聊天来查看详细信息</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { apiClient } from '../utils/api-new'

// 路由
const router = useRouter()

// 响应式数据
const chats = ref<any[]>([])
const isLoading = ref(false)
const searchQuery = ref('')
const activeCategory = ref('all')
const selectedChat = ref<any>(null)

// 计算属性
const filteredChats = computed(() => {
  let filtered = chats.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(chat =>
      chat.title.toLowerCase().includes(query) ||
      (chat.username && chat.username.toLowerCase().includes(query))
    )
  }

  // 类型过滤
  if (activeCategory.value !== 'all') {
    filtered = filtered.filter(chat => {
      switch (activeCategory.value) {
        case 'groups':
          return chat.type === 'group' || chat.type === 'supergroup'
        case 'channels':
          return chat.type === 'channel'
        case 'private':
          return chat.type === 'private'
        case 'bots':
          return chat.type === 'bot'
        default:
          return true
      }
    })
  }

  // 按最后消息时间排序
  return filtered.sort((a, b) => {
    const dateA = new Date(a.last_message_date || 0).getTime()
    const dateB = new Date(b.last_message_date || 0).getTime()
    return dateB - dateA
  })
})

// 置顶聊天
const pinnedChats = computed(() =>
  filteredChats.value.filter(chat => chat.is_pinned)
)

// 普通聊天
const regularChats = computed(() =>
  filteredChats.value.filter(chat => !chat.is_pinned)
)

// 统计数据
const totalChats = computed(() => chats.value.length)
const groupCount = computed(() =>
  chats.value.filter(chat => chat.type === 'group' || chat.type === 'supergroup').length
)
const channelCount = computed(() =>
  chats.value.filter(chat => chat.type === 'channel').length
)
const privateCount = computed(() =>
  chats.value.filter(chat => chat.type === 'private').length
)
const botCount = computed(() =>
  chats.value.filter(chat => chat.type === 'bot').length
)

// 方法
const loadChats = async () => {
  try {
    console.log('📡 开始加载聊天列表...')
    isLoading.value = true
    const response = await apiClient.getChats()

    console.log('📊 聊天列表响应:', response)

    if (response.success && response.data) {
      chats.value = response.data
      console.log(`✅ 成功加载 ${chats.value.length} 个聊天`)

      // 如果有统计信息，也打印出来
      if (response.stats) {
        console.log('📈 聊天统计:', response.stats)
      }
    } else {
      console.warn('⚠️ 聊天列表响应格式异常:', response)
      chats.value = []
    }
  } catch (error) {
    console.error('❌ 加载聊天列表失败:', error)
    chats.value = []
  } finally {
    isLoading.value = false
  }
}

const syncChats = async () => {
  try {
    console.log('🔄 开始同步Telegram聊天...')
    isLoading.value = true

    // 直接调用获取聊天列表API（它会从Telegram实时获取）
    await loadChats()

    console.log('✅ 聊天同步完成')
  } catch (error) {
    console.error('❌ 同步聊天失败:', error)
  } finally {
    isLoading.value = false
  }
}

const setCategory = (category: string) => {
  activeCategory.value = category
  selectedChat.value = null // 切换分类时清除选中状态
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
  selectedChat.value = null // 搜索时清除选中状态
}

const selectChat = (chat: any) => {
  selectedChat.value = chat
  console.log('📱 选中聊天:', chat.title)
}

const openChatMessages = (chat: any) => {
  router.push(`/chats/${chat.telegram_chat_id || chat.id}`)
}

const downloadHistory = async (chat: any) => {
  try {
    console.log('📥 下载聊天历史:', chat.title)
    // TODO: 实现下载历史功能
    alert(`开始下载 ${chat.title} 的聊天历史...`)
  } catch (error) {
    console.error('❌ 下载历史失败:', error)
  }
}

const toggleMute = async (chat: any) => {
  try {
    console.log('🔇 切换静音状态:', chat.title)
    // TODO: 实现静音功能
    chat.is_muted = !chat.is_muted
  } catch (error) {
    console.error('❌ 切换静音失败:', error)
  }
}

const handleAvatarError = (event: any) => {
  console.warn('⚠️ 头像加载失败，使用默认头像')
  event.target.style.display = 'none'
}

// 工具函数
const getCategoryTitle = () => {
  switch (activeCategory.value) {
    case 'private':
      return '联系人'
    case 'groups':
      return '群组'
    case 'channels':
      return '频道'
    case 'bots':
      return '机器人'
    default:
      return '所有聊天'
  }
}

const getEmptyIcon = () => {
  switch (activeCategory.value) {
    case 'private':
      return '👤'
    case 'groups':
      return '👥'
    case 'channels':
      return '📢'
    case 'bots':
      return '🤖'
    default:
      return '💬'
  }
}

const getEmptyTitle = () => {
  switch (activeCategory.value) {
    case 'private':
      return '没有联系人'
    case 'groups':
      return '没有群组'
    case 'channels':
      return '没有频道'
    case 'bots':
      return '没有机器人'
    default:
      return '没有聊天'
  }
}

const getEmptyMessage = () => {
  if (searchQuery.value) {
    return `没有找到匹配 "${searchQuery.value}" 的聊天`
  }
  switch (activeCategory.value) {
    case 'private':
      return '您还没有私人聊天'
    case 'groups':
      return '您还没有加入任何群组'
    case 'channels':
      return '您还没有订阅任何频道'
    case 'bots':
      return '您还没有与机器人聊天'
    default:
      return '开始同步您的Telegram聊天'
  }
}

const formatMemberCount = (count: number) => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  }
  return count.toString()
}

const formatLastTime = (dateString: string) => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const getChatTypeClass = (type: string) => {
  switch (type) {
    case 'channel':
      return 'channel'
    case 'group':
    case 'supergroup':
      return 'group'
    case 'private':
      return 'private'
    default:
      return 'default'
  }
}

const getChatInitial = (title: string) => {
  return title.charAt(0).toUpperCase()
}

const formatChatType = (type: string) => {
  switch (type) {
    case 'channel':
      return 'Channel'
    case 'group':
      return 'Group'
    case 'supergroup':
      return 'Supergroup'
    case 'private':
      return 'Private'
    default:
      return type
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return 'Today'
  } else if (days === 1) {
    return 'Yesterday'
  } else if (days < 7) {
    return `${days} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

// 生命周期
onMounted(() => {
  loadChats()
})
</script>

<style scoped>
.chats-page {
  display: flex;
  height: calc(100vh - 80px); /* 减去顶部导航栏高度 */
  background: #f8fafc;
}

/* 左侧聊天列表 */
.chats-sidebar {
  width: 400px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-search {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.sidebar-search i {
  position: absolute;
  left: 28px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 16px;
}

.sidebar-search input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.sidebar-search input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sidebar-actions {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.sync-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.sync-btn:hover:not(:disabled) {
  background: #2563eb;
}

.sync-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

.category-tabs {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.category-tab:hover {
  background: #f3f4f6;
}

.category-tab.active {
  background: #eff6ff;
  color: #3b82f6;
}

.category-tab i {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.chats-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.chat-group {
  margin-bottom: 16px;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.chat-item:hover {
  background: #f9fafb;
}

.chat-item.active {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.chat-avatar {
  position: relative;
  flex-shrink: 0;
}

.chat-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.avatar-placeholder.channel {
  background: #3b82f6;
}

.avatar-placeholder.group,
.avatar-placeholder.supergroup {
  background: #22c55e;
}

.avatar-placeholder.private {
  background: #8b5cf6;
}

.avatar-placeholder.bot {
  background: #f59e0b;
}

.avatar-placeholder.default {
  background: #6b7280;
}

.unread-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.chat-type {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.member-count,
.username {
  color: #9ca3af;
}

.chat-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

.last-time {
  font-size: 11px;
  color: #9ca3af;
}

.icon-mute {
  color: #6b7280;
  font-size: 14px;
}

/* 右侧聊天详情 */
.chat-detail {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  text-align: center;
}

.no-chat-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-chat-selected h3 {
  font-size: 20px;
  color: #111827;
  margin: 0 0 8px;
}

.no-chat-selected p {
  margin: 0;
  font-size: 14px;
}

.chat-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.chat-avatar-large {
  flex-shrink: 0;
}

.chat-avatar-large img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 600;
  color: white;
}

.chat-header-info {
  flex: 1;
  min-width: 0;
}

.chat-title-large {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px;
  word-break: break-word;
}

.chat-meta-large {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.chat-type-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chat-type-badge.channel {
  background: #dbeafe;
  color: #1d4ed8;
}

.chat-type-badge.group,
.chat-type-badge.supergroup {
  background: #dcfce7;
  color: #166534;
}

.chat-type-badge.private {
  background: #ede9fe;
  color: #7c3aed;
}

.chat-type-badge.bot {
  background: #fef3c7;
  color: #d97706;
}

.username-large {
  color: #6b7280;
  font-size: 14px;
}

.member-count-large {
  color: #6b7280;
  font-size: 14px;
}

.chat-stats-large {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
}

.stat-item i {
  width: 16px;
  text-align: center;
}

.chat-actions-large {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn-large {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.action-btn-large:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #f8fafc;
}

.action-btn-large i {
  font-size: 16px;
}

.chat-statistics {
  margin-top: 24px;
}

.chat-statistics h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.empty-state,
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  font-size: 18px;
  color: #111827;
  margin: 0 0 8px;
}

.empty-state p {
  margin: 0 0 16px;
  font-size: 14px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标 */
.icon-sync::before { content: "🔄"; }
.icon-search::before { content: "🔍"; }
.icon-all::before { content: "💬"; }
.icon-user::before { content: "👤"; }
.icon-group::before { content: "👥"; }
.icon-channel::before { content: "📢"; }
.icon-bot::before { content: "🤖"; }
.icon-pin::before { content: "📌"; }
.icon-chat::before { content: "💬"; }
.icon-message::before { content: "💬"; }
.icon-download::before { content: "⬇️"; }
.icon-time::before { content: "🕐"; }
.icon-mute::before { content: "🔇"; }
.icon-unmute::before { content: "🔊"; }

/* 响应式设计 */
@media (max-width: 1024px) {
  .chats-sidebar {
    width: 350px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .chats-page {
    flex-direction: column;
    height: auto;
  }

  .chats-sidebar {
    width: 100%;
    height: 50vh;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .chat-detail {
    height: 50vh;
  }

  .category-tabs {
    flex-direction: row;
    overflow-x: auto;
    padding: 12px 16px;
  }

  .category-tab {
    flex-shrink: 0;
    padding: 8px 12px;
    font-size: 12px;
  }

  .chat-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 16px;
  }

  .chat-actions-large {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .chats-sidebar {
    height: 60vh;
  }

  .chat-detail {
    height: 40vh;
  }

  .chat-item {
    padding: 8px 12px;
  }

  .chat-avatar img,
  .avatar-placeholder {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .chat-title {
    font-size: 14px;
  }

  .chat-meta {
    font-size: 11px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background: #2563eb;
}

.filters-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-box input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.chats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.chat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 16px;
}

.chat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chat-avatar {
  flex-shrink: 0;
}

.avatar-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.avatar-placeholder.channel {
  background: #3b82f6;
}

.avatar-placeholder.group {
  background: #22c55e;
}

.avatar-placeholder.private {
  background: #8b5cf6;
}

.avatar-placeholder.default {
  background: #6b7280;
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.chat-type {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.chat-date {
  font-size: 12px;
  color: #9ca3af;
}

.chat-stats {
  font-size: 12px;
  color: #6b7280;
}

.archived-badge {
  background: #fbbf24;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  margin-left: 8px;
}

.chat-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
}

.empty-state,
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #111827;
  margin: 0 0 8px;
}

.empty-state p {
  margin: 0 0 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标 */
.icon-sync::before { content: "🔄"; }
.icon-search::before { content: "🔍"; }
.icon-download::before { content: "⬇️"; }
.icon-archive::before { content: "📦"; }
.icon-unarchive::before { content: "📤"; }
.icon-more::before { content: "⋯"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .chats-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-buttons {
    justify-content: center;
  }
}
</style>
