<template>
  <div class="analytics-page">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">💬</div>
        <div class="stat-content">
          <div class="stat-value">{{ totalMessages.toLocaleString() }}</div>
          <div class="stat-label">总消息数</div>
          <div class="stat-change" v-if="totalMessages > 0">--</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <div class="stat-value">{{ totalChats }}</div>
          <div class="stat-label">活跃聊天</div>
          <div class="stat-change" v-if="totalChats > 0">--</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">📈</div>
        <div class="stat-content">
          <div class="stat-value">{{ dailyAverage }}</div>
          <div class="stat-label">日均消息</div>
          <div class="stat-change" v-if="dailyAverage > 0">--</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">🔔</div>
        <div class="stat-content">
          <div class="stat-value">{{ alertsTriggered }}</div>
          <div class="stat-label">关键词提醒</div>
          <div class="stat-change" v-if="alertsTriggered > 0">--</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <div class="chart-header">
          <h3>消息趋势</h3>
          <div class="chart-controls">
            <select v-model="timeRange" class="time-select">
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
            </select>
          </div>
        </div>
        <div class="chart-placeholder">
          <div class="chart-mock">
            📊 消息数量趋势图
            <p>显示选定时间范围内的消息数量变化</p>
          </div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-header">
          <h3>聊天活跃度</h3>
        </div>
        <div class="chart-placeholder">
          <div class="chart-mock">
            🥧 聊天活跃度分布
            <p>各个聊天的消息占比</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细统计 -->
    <div class="detailed-stats">
      <div class="stats-section">
        <h3>热门聊天</h3>
        <div class="chat-stats-list">
          <div v-for="chat in topChats" :key="chat.id" class="chat-stat-item">
            <div class="chat-info">
              <div class="chat-avatar">{{ chat.name.charAt(0) }}</div>
              <div class="chat-details">
                <div class="chat-name">{{ chat.name }}</div>
                <div class="chat-type">{{ chat.type }}</div>
              </div>
            </div>
            <div class="chat-metrics">
              <div class="metric">
                <span class="metric-value">{{ chat.messages }}</span>
                <span class="metric-label">消息</span>
              </div>
              <div class="metric">
                <span class="metric-value">{{ chat.percentage }}%</span>
                <span class="metric-label">占比</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <h3>关键词统计</h3>
        <div class="keyword-stats">
          <div v-for="keyword in topKeywords" :key="keyword.word" class="keyword-item">
            <div class="keyword-text">{{ keyword.word }}</div>
            <div class="keyword-count">{{ keyword.count }}</div>
            <div class="keyword-bar">
              <div class="keyword-progress" :style="{ width: keyword.percentage + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间分析 -->
    <div class="time-analysis">
      <h3>活跃时间分析</h3>
      <div class="heatmap-container">
        <div class="heatmap-placeholder">
          <div class="heatmap-mock">
            🕐 24小时活跃度热力图
            <p>显示一天中不同时间段的消息活跃度</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const timeRange = ref('30d')

// 真实统计数据 - 从后端API获取
const totalMessages = ref(0)
const totalChats = ref(0)
const dailyAverage = ref(0)
const alertsTriggered = ref(0)

// 热门聊天数据 - 从后端API获取
const topChats = ref([])

// 热门关键词 - 从后端API获取
const topKeywords = ref([])
</script>

<style scoped>
.analytics-page {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #059669;
}

.stat-change.negative {
  color: #dc2626;
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.time-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

.chart-mock {
  text-align: center;
  color: #6b7280;
  font-size: 18px;
}

.chart-mock p {
  margin: 8px 0 0;
  font-size: 14px;
}

.detailed-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.stats-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px;
}

.chat-stats-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.chat-name {
  font-weight: 500;
  color: #111827;
}

.chat-type {
  font-size: 12px;
  color: #6b7280;
}

.chat-metrics {
  display: flex;
  gap: 16px;
}

.metric {
  text-align: center;
}

.metric-value {
  display: block;
  font-weight: 600;
  color: #111827;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
}

.keyword-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.keyword-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.keyword-text {
  min-width: 80px;
  font-weight: 500;
  color: #111827;
}

.keyword-count {
  min-width: 40px;
  font-size: 14px;
  color: #6b7280;
}

.keyword-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.keyword-progress {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.time-analysis {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-analysis h3 {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px;
}

.heatmap-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

.heatmap-mock {
  text-align: center;
  color: #6b7280;
  font-size: 18px;
}

.heatmap-mock p {
  margin: 8px 0 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charts-section,
  .detailed-stats {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chat-metrics {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
