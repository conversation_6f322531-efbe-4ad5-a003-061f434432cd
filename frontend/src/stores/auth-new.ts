/**
 * 现代化认证状态管理 - 基于README.md规范
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient } from '../utils/api'

// 类型定义
interface User {
  user_id: number
  telegram_user_id: number
  username?: string
  first_name?: string
  last_name?: string
  phone_number?: string
  created_at: string
  updated_at: string
}

interface AuthResponse {
  access_token: string
  user_id: number
  token_type: string
}

interface LoginRequest {
  phone_number?: string
  api_id?: number
  api_hash?: string
  phone_code_hash?: string
  code?: string
  password?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const accessToken = ref<string | null>(null)
  const isAuthenticated = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 登录流程状态
  const authStep = ref<'phone' | 'code' | 'password' | 'success'>('phone')
  const phoneCodeHash = ref<string | null>(null)
  const requiresPassword = ref(false)

  // 计算属性
  const currentUser = computed(() => user.value)
  const isLoggedIn = computed(() => isAuthenticated.value && !!accessToken.value)

  // 存储键
  const STORAGE_KEYS = {
    ACCESS_TOKEN: 'teleseeker_access_token',
    USER: 'teleseeker_user',
    AUTH_STATE: 'teleseeker_auth_state'
  }

  // 方法
  const setError = (message: string) => {
    error.value = message
    console.error('[Auth]', message)
  }

  const clearError = () => {
    error.value = null
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  // 请求验证码
  const requestCode = async (phoneNumber: string): Promise<string> => {
    try {
      setLoading(true)
      clearError()
      
      const response = await apiClient.post('/auth/request_code', {
        phone_number: phoneNumber
      })

      phoneCodeHash.value = response.phone_code_hash
      authStep.value = 'code'

      return response.phone_code_hash
    } catch (err: any) {
      const message = err.response?.data?.detail || '请求验证码失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 手机验证码登录
  const loginWithPhone = async (phoneNumber: string, code: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()

      if (!phoneCodeHash.value) {
        throw new Error('请先请求验证码')
      }

      const response = await apiClient.post('/auth/login_phone', {
        phone_number: phoneNumber,
        phone_code_hash: phoneCodeHash.value,
        code: code
      })

      await handleAuthSuccess(response)
    } catch (err: any) {
      const message = err.response?.data?.detail || '登录失败'
      
      if (message.includes('两步验证')) {
        requiresPassword.value = true
        authStep.value = 'password'
      } else {
        setError(message)
      }
      
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 密码登录（两步验证）
  const loginWithPassword = async (password: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()

      const response = await apiClient.post('/auth/login_password', {
        password: password
      })

      await handleAuthSuccess(response)
    } catch (err: any) {
      const message = err.response?.data?.detail || '密码验证失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // API ID/Hash 登录
  const loginWithApiId = async (apiId: number, apiHash: string): Promise<void> => {
    try {
      setLoading(true)
      clearError()

      const response = await apiClient.post('/auth/login_api_id', {
        api_id: apiId,
        api_hash: apiHash
      })

      await handleAuthSuccess(response)
    } catch (err: any) {
      const message = err.response?.data?.detail || 'API登录失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 高级登录：使用API凭据请求验证码（临时使用普通请求验证码接口）
  const requestCodeWithApi = async (phoneNumber: string, apiId: number, apiHash: string): Promise<string> => {
    try {
      console.log('[Auth] 🚀 开始高级登录请求验证码')
      console.log('[Auth] 📱 手机号:', phoneNumber)
      console.log('[Auth] 🔑 API ID:', apiId)
      console.log('[Auth] 🔐 API Hash长度:', apiHash?.length || 0)

      setLoading(true)
      clearError()

      // 临时方案：先存储API凭据到localStorage，然后使用普通的请求验证码接口
      localStorage.setItem('temp_api_id', apiId.toString())
      localStorage.setItem('temp_api_hash', apiHash)

      const response = await apiClient.post('/auth/request_code', {
        phone_number: phoneNumber,
        api_id: apiId,
        api_hash: apiHash
      })

      phoneCodeHash.value = response.phone_code_hash
      authStep.value = 'code'

      console.log('[Auth] ✅ 高级登录请求验证码成功（使用临时方案）')
      return response.phone_code_hash
    } catch (err: any) {
      console.error('[Auth] ❌ 高级登录请求验证码失败:', err)
      const message = err.response?.data?.detail || '请求验证码失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 高级登录：使用API凭据和验证码登录（临时使用普通登录接口）
  const loginWithPhoneAndApi = async (phoneNumber: string, code: string, apiId: number, apiHash: string): Promise<void> => {
    try {
      console.log('[Auth] 🚀 开始高级登录验证码验证')
      setLoading(true)
      clearError()

      if (!phoneCodeHash.value) {
        throw new Error('请先请求验证码')
      }

      // 临时方案：使用普通的登录接口，API凭据已存储在localStorage中
      const response = await apiClient.post('/auth/login_phone', {
        phone_number: phoneNumber,
        phone_code_hash: phoneCodeHash.value,
        code: code
      })

      // 在成功后，将API凭据信息添加到用户数据中
      const userData = response
      if (userData.user) {
        userData.user.api_id = apiId
        userData.user.api_hash = apiHash
      }

      await handleAuthSuccess(userData)
      console.log('[Auth] ✅ 高级登录验证码验证成功')
    } catch (err: any) {
      console.error('[Auth] ❌ 高级登录验证码验证失败:', err)
      const message = err.response?.data?.detail || '登录失败'

      if (message.includes('两步验证') || message.includes('password')) {
        requiresPassword.value = true
        authStep.value = 'password'
        // 保存API凭据以便密码验证时使用
        localStorage.setItem('temp_api_id', apiId.toString())
        localStorage.setItem('temp_api_hash', apiHash)
      } else {
        setError(message)
      }

      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 高级登录：使用API凭据和密码验证（临时使用普通密码验证接口）
  const loginWithPasswordAndApi = async (password: string, apiId: number, apiHash: string): Promise<void> => {
    try {
      console.log('[Auth] 🚀 开始高级登录密码验证')
      setLoading(true)
      clearError()

      // 使用真实的密码验证接口
      const response = await apiClient.post('/auth/login_password', {
        password: password,
        phone_code_hash: phoneCodeHash.value
      })

      // 在成功后，将API凭据信息添加到用户数据中
      const userData = response
      if (userData.user) {
        userData.user.api_id = apiId
        userData.user.api_hash = apiHash
      }

      await handleAuthSuccess(userData)

      // 清理临时存储的API凭据
      localStorage.removeItem('temp_api_id')
      localStorage.removeItem('temp_api_hash')

      console.log('[Auth] ✅ 高级登录密码验证成功')
    } catch (err: any) {
      console.error('[Auth] ❌ 高级登录密码验证失败:', err)
      const message = err.response?.data?.detail || '密码验证失败'
      setError(message)
      throw new Error(message)
    } finally {
      setLoading(false)
    }
  }

  // 处理认证成功
  const handleAuthSuccess = async (authResponse: AuthResponse): Promise<void> => {
    try {
      // 保存令牌
      accessToken.value = authResponse.access_token
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, authResponse.access_token)

      // 获取用户信息
      const userResponse = await apiClient.get('/auth/me')
      user.value = userResponse
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(userResponse))

      // 更新状态
      isAuthenticated.value = true
      authStep.value = 'success'
      
      // 保存认证状态
      const authState = {
        isAuthenticated: true,
        timestamp: Date.now()
      }
      localStorage.setItem(STORAGE_KEYS.AUTH_STATE, JSON.stringify(authState))

      console.log('[Auth] 登录成功', user.value)
    } catch (err) {
      console.error('[Auth] 获取用户信息失败', err)
      await logout()
      throw err
    }
  }

  // 注销
  const logout = async (): Promise<void> => {
    try {
      // 调用后端注销接口
      if (accessToken.value) {
        await apiClient.post('/auth/logout')
      }
    } catch (err) {
      console.warn('[Auth] 后端注销失败', err)
    } finally {
      // 清除本地状态
      user.value = null
      accessToken.value = null
      isAuthenticated.value = false
      phoneCodeHash.value = null
      requiresPassword.value = false
      authStep.value = 'phone'
      
      // 清除本地存储
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
      localStorage.removeItem(STORAGE_KEYS.USER)
      localStorage.removeItem(STORAGE_KEYS.AUTH_STATE)
      
      console.log('[Auth] 已注销')
    }
  }

  // 初始化认证状态
  const initializeAuth = async (): Promise<boolean> => {
    try {
      setLoading(true)
      
      // 从本地存储恢复状态
      const storedToken = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
      const storedUser = localStorage.getItem(STORAGE_KEYS.USER)
      const storedAuthState = localStorage.getItem(STORAGE_KEYS.AUTH_STATE)
      
      if (!storedToken || !storedUser || !storedAuthState) {
        return false
      }

      // 检查认证状态是否过期（24小时）
      const authState = JSON.parse(storedAuthState)
      const isExpired = Date.now() - authState.timestamp > 24 * 60 * 60 * 1000
      
      if (isExpired) {
        await logout()
        return false
      }

      // 恢复状态
      accessToken.value = storedToken
      user.value = JSON.parse(storedUser)
      isAuthenticated.value = true

      // 验证令牌有效性
      try {
        await apiClient.get('/auth/me')
        console.log('[Auth] 认证状态已恢复')
        return true
      } catch (err) {
        console.warn('[Auth] 令牌无效，清除认证状态')
        await logout()
        return false
      }
    } catch (err) {
      console.error('[Auth] 初始化认证状态失败', err)
      await logout()
      return false
    } finally {
      setLoading(false)
    }
  }

  // 刷新用户信息
  const refreshUser = async (): Promise<void> => {
    try {
      if (!isAuthenticated.value) {
        throw new Error('用户未认证')
      }

      const response = await apiClient.get('/auth/me')
      user.value = response
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response))
    } catch (err) {
      console.error('[Auth] 刷新用户信息失败', err)
      throw err
    }
  }

  // 重置认证流程
  const resetAuthFlow = (): void => {
    authStep.value = 'phone'
    phoneCodeHash.value = null
    requiresPassword.value = false
    clearError()
  }

  return {
    // 状态
    user: computed(() => user.value),
    accessToken: computed(() => accessToken.value),
    isAuthenticated: computed(() => isAuthenticated.value),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    authStep: computed(() => authStep.value),
    requiresPassword: computed(() => requiresPassword.value),
    
    // 计算属性
    currentUser,
    isLoggedIn,
    
    // 方法
    requestCode,
    requestCodeWithApi,
    loginWithPhone,
    loginWithPhoneAndApi,
    loginWithPassword,
    loginWithPasswordAndApi,
    loginWithApiId,
    logout,
    initializeAuth,
    refreshUser,
    resetAuthFlow,
    clearError
  }
})
