<template>
  <div class="main-layout">
    <!-- 左侧导航栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <h2>TeleSeeker</h2>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-section">
          <h3 class="nav-section-title">PAGES</h3>
          <ul class="nav-list">
            <li class="nav-item" :class="{ active: currentRoute === 'dashboard' }">
              <router-link to="/dashboard" class="nav-link">
                <i class="icon-dashboard"></i>
                <span>Dashboard</span>
              </router-link>
            </li>
            <li class="nav-item" :class="{ active: currentRoute === 'chats' || showChatsList }">
              <div class="nav-link" @click="toggleChatsList">
                <i class="icon-chat"></i>
                <span>Chats</span>
                <span v-if="unreadCount > 0" class="badge">{{ unreadCount }}</span>
                <i class="expand-icon" :class="{ expanded: showChatsList }">▼</i>
              </div>

              <!-- Chats子项 -->
              <div v-if="showChatsList" class="chat-categories-container">
                <!-- 群组 -->
                <div class="chat-category-item">
                  <div class="category-header" @click="toggleCategory('groups')">
                    <i class="category-icon">👥</i>
                    <span class="category-label">群组</span>
                    <span v-if="groupsCount > 0" class="category-count">{{ groupsCount }}</span>
                    <i class="expand-icon" :class="{ expanded: expandedCategories.groups }">▼</i>
                  </div>
                  <div v-if="expandedCategories.groups" class="category-chats">
                    <div v-if="isLoadingChats" class="loading-state">
                      <div class="loading-spinner"></div>
                      <span>加载群组...</span>
                    </div>
                    <div v-else-if="groupChats.length === 0" class="empty-state">
                      <span>暂无群组</span>
                    </div>
                    <div v-else>
                      <div
                        v-for="chat in groupChats"
                        :key="chat.id"
                        @click="selectChat(chat)"
                        :class="['chat-item', { active: selectedChatId === chat.id }]"
                      >
                        <div class="chat-avatar">
                          <img
                            v-if="chat.avatar_url"
                            :src="chat.avatar_url"
                            :alt="chat.title"
                            @error="handleAvatarError"
                          />
                          <div v-else class="avatar-placeholder avatar-group">
                            {{ getChatInitial(chat.title) }}
                          </div>
                          <div v-if="chat.unread_count > 0" class="unread-badge">
                            {{ chat.unread_count > 99 ? '99+' : chat.unread_count }}
                          </div>
                        </div>
                        <div class="chat-info">
                          <div class="chat-title">{{ chat.title }}</div>
                          <div class="chat-members">{{ chat.participants_count || 0 }} 成员</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 频道 -->
                <div class="chat-category-item">
                  <div class="category-header" @click="toggleCategory('channels')">
                    <i class="category-icon">📢</i>
                    <span class="category-label">频道</span>
                    <span v-if="channelsCount > 0" class="category-count">{{ channelsCount }}</span>
                    <i class="expand-icon" :class="{ expanded: expandedCategories.channels }">▼</i>
                  </div>
                  <div v-if="expandedCategories.channels" class="category-chats">
                    <div v-if="isLoadingChats" class="loading-state">
                      <div class="loading-spinner"></div>
                      <span>加载频道...</span>
                    </div>
                    <div v-else-if="channelChats.length === 0" class="empty-state">
                      <span>暂无频道</span>
                    </div>
                    <div v-else>
                      <div
                        v-for="chat in channelChats"
                        :key="chat.id"
                        @click="selectChat(chat)"
                        :class="['chat-item', { active: selectedChatId === chat.id }]"
                      >
                        <div class="chat-avatar">
                          <img
                            v-if="chat.avatar_url"
                            :src="chat.avatar_url"
                            :alt="chat.title"
                            @error="handleAvatarError"
                          />
                          <div v-else class="avatar-placeholder avatar-channel">
                            {{ getChatInitial(chat.title) }}
                          </div>
                          <div v-if="chat.unread_count > 0" class="unread-badge">
                            {{ chat.unread_count > 99 ? '99+' : chat.unread_count }}
                          </div>
                        </div>
                        <div class="chat-info">
                          <div class="chat-title">{{ chat.title }}</div>
                          <div class="chat-members">{{ chat.participants_count || 0 }} 订阅者</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 联系人 -->
                <div class="chat-category-item">
                  <div class="category-header" @click="toggleCategory('contacts')">
                    <i class="category-icon">👤</i>
                    <span class="category-label">联系人</span>
                    <span v-if="contactsCount > 0" class="category-count">{{ contactsCount }}</span>
                    <i class="expand-icon" :class="{ expanded: expandedCategories.contacts }">▼</i>
                  </div>
                  <div v-if="expandedCategories.contacts" class="category-chats">
                    <div v-if="isLoadingChats" class="loading-state">
                      <div class="loading-spinner"></div>
                      <span>加载联系人...</span>
                    </div>
                    <div v-else-if="contactChats.length === 0" class="empty-state">
                      <span>暂无联系人</span>
                    </div>
                    <div v-else>
                      <div
                        v-for="chat in contactChats"
                        :key="chat.id"
                        @click="selectChat(chat)"
                        :class="['chat-item', { active: selectedChatId === chat.id }]"
                      >
                        <div class="chat-avatar">
                          <img
                            v-if="chat.avatar_url"
                            :src="chat.avatar_url"
                            :alt="chat.title"
                            @error="handleAvatarError"
                          />
                          <div v-else class="avatar-placeholder avatar-private">
                            {{ getChatInitial(chat.title) }}
                          </div>
                          <div v-if="chat.unread_count > 0" class="unread-badge">
                            {{ chat.unread_count > 99 ? '99+' : chat.unread_count }}
                          </div>
                        </div>
                        <div class="chat-info">
                          <div class="chat-title">{{ chat.title }}</div>
                          <div class="chat-status">{{ chat.username ? '@' + chat.username : '联系人' }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 机器人 -->
                <div class="chat-category-item">
                  <div class="category-header" @click="toggleCategory('bots')">
                    <i class="category-icon">🤖</i>
                    <span class="category-label">机器人</span>
                    <span v-if="botsCount > 0" class="category-count">{{ botsCount }}</span>
                    <i class="expand-icon" :class="{ expanded: expandedCategories.bots }">▼</i>
                  </div>
                  <div v-if="expandedCategories.bots" class="category-chats">
                    <div v-if="isLoadingChats" class="loading-state">
                      <div class="loading-spinner"></div>
                      <span>加载机器人...</span>
                    </div>
                    <div v-else-if="botChats.length === 0" class="empty-state">
                      <span>暂无机器人</span>
                    </div>
                    <div v-else>
                      <div
                        v-for="chat in botChats"
                        :key="chat.id"
                        @click="selectChat(chat)"
                        :class="['chat-item', { active: selectedChatId === chat.id }]"
                      >
                        <div class="chat-avatar">
                          <img
                            v-if="chat.avatar_url"
                            :src="chat.avatar_url"
                            :alt="chat.title"
                            @error="handleAvatarError"
                          />
                          <div v-else class="avatar-placeholder avatar-bot">
                            {{ getChatInitial(chat.title) }}
                          </div>
                          <div v-if="chat.unread_count > 0" class="unread-badge">
                            {{ chat.unread_count > 99 ? '99+' : chat.unread_count }}
                          </div>
                        </div>
                        <div class="chat-info">
                          <div class="chat-title">{{ chat.title }}</div>
                          <div class="chat-status">机器人</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 用户自定义分组 -->
                <div class="chat-category-item" v-if="customGroups.length > 0">
                  <div class="category-header" @click="toggleCategory('custom')">
                    <i class="category-icon">📁</i>
                    <span class="category-label">自定义分组</span>
                    <span v-if="customGroups.length > 0" class="category-count">{{ customGroups.length }}</span>
                    <i class="expand-icon" :class="{ expanded: expandedCategories.custom }">▼</i>
                  </div>
                  <div v-if="expandedCategories.custom" class="category-chats">
                    <div v-if="isLoadingChats" class="loading-state">
                      <div class="loading-spinner"></div>
                      <span>加载自定义分组...</span>
                    </div>
                    <div v-else>
                      <div
                        v-for="group in customGroups"
                        :key="group.id"
                        @click="selectCustomGroup(group)"
                        :class="['chat-item', { active: selectedCustomGroupId === group.id }]"
                      >
                        <div class="chat-avatar">
                          <div class="avatar-placeholder avatar-custom">
                            {{ group.title.charAt(0) }}
                          </div>
                          <div v-if="group.chat_count > 0" class="unread-badge">
                            {{ group.chat_count }}
                          </div>
                        </div>
                        <div class="chat-info">
                          <div class="chat-title">{{ group.title }}</div>
                          <div class="chat-status">{{ group.chat_count }} 个聊天</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
            <li class="nav-item" :class="{ active: currentRoute === 'search' }">
              <router-link to="/search" class="nav-link">
                <i class="icon-search"></i>
                <span>Search</span>
              </router-link>
            </li>
            <li class="nav-item" :class="{ active: currentRoute === 'analytics' }">
              <router-link to="/analytics" class="nav-link">
                <i class="icon-analytics"></i>
                <span>Analytics</span>
              </router-link>
            </li>
          </ul>
        </div>
        
        <div class="nav-section">
          <h3 class="nav-section-title">MORE</h3>
          <ul class="nav-list">
            <li class="nav-item" :class="{ active: currentRoute === 'alerts' }">
              <router-link to="/alerts" class="nav-link">
                <i class="icon-bell"></i>
                <span>Keyword Alerts</span>
              </router-link>
            </li>
            <li class="nav-item" :class="{ active: currentRoute === 'settings' }">
              <router-link to="/settings" class="nav-link">
                <i class="icon-settings"></i>
                <span>Settings</span>
              </router-link>
            </li>
          </ul>
        </div>
      </nav>
    </aside>
    
    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <h1 class="page-title">{{ pageTitle }}</h1>
          <span class="page-subtitle">{{ pageSubtitle }}</span>
        </div>
        
        <div class="header-right">
          <!-- 搜索框 -->
          <div class="search-box">
            <i class="icon-search"></i>
            <input 
              type="text" 
              placeholder="Search..." 
              v-model="searchQuery"
              @keyup.enter="handleSearch"
            />
          </div>
          
          <!-- 通知 -->
          <div class="notification-icon" @click="toggleNotifications">
            <i class="icon-bell"></i>
            <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
          </div>
          
          <!-- 用户菜单 -->
          <div class="user-menu" @click="toggleUserMenu">
            <div class="user-avatar">
              <img :src="userAvatar" :alt="userName" />
            </div>
            <div class="user-info">
              <span class="user-name">{{ userName }}</span>
            </div>
            <i class="icon-chevron-down"></i>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <div class="page-content">
        <slot />
      </div>
    </main>
    
    <!-- 通知面板 -->
    <div v-if="showNotifications" class="notification-panel">
      <div class="notification-header">
        <h3>Notifications</h3>
        <button @click="markAllAsRead">Mark all as read</button>
      </div>
      <div class="notification-list">
        <div v-for="notification in notifications" :key="notification.id" class="notification-item">
          <div class="notification-content">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.message }}</p>
            <span class="notification-time">{{ formatTime(notification.time) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户菜单下拉 -->
    <div v-if="showUserMenu" class="user-menu-dropdown">
      <ul>
        <li @click="goToProfile">Profile</li>
        <li @click="goToSettings">Settings</li>
        <li class="divider"></li>
        <li @click="logout">Logout</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth-new'
import { apiClient } from '@/utils/api-new'

// 路由和认证
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const searchQuery = ref('')
const showNotifications = ref(false)
const showUserMenu = ref(false)
// 真实数据 - 从后端API获取
const unreadCount = ref(0)
const notificationCount = ref(0)
const notifications = ref([])

// 聊天列表相关数据
const showChatsList = ref(false)
const isLoadingChats = ref(false)
const chats = ref([])
const selectedChatId = ref(null)
const selectedCustomGroupId = ref(null)
const customGroups = ref([])
// 分类展开状态 - 默认展开群组
const expandedCategories = ref({
  groups: true,    // 默认展开群组
  channels: false,
  contacts: false,
  bots: false,
  custom: false
})

// Computed
const currentRoute = computed(() => route.name as string)
const pageTitle = computed(() => {
  const titles: Record<string, string> = {
    dashboard: 'Dashboard',
    chats: 'Chats',
    search: 'Search',
    analytics: 'Analytics',
    alerts: 'Keyword Alerts',
    settings: 'Settings'
  }
  return titles[currentRoute.value] || 'TeleSeeker'
})

const pageSubtitle = computed(() => {
  const subtitles: Record<string, string> = {
    dashboard: new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    chats: 'Manage your Telegram conversations',
    search: 'Find messages across all chats',
    analytics: 'View your chat statistics',
    alerts: 'Monitor keywords in real-time',
    settings: 'Configure your preferences'
  }
  return subtitles[currentRoute.value] || ''
})

// 用户信息 - 使用真实的当前登录用户数据
const userName = computed(() => {
  const user = authStore.currentUser
  console.log('🔍 当前用户数据:', user)

  // 检查用户数据结构
  if (user?.first_name) {
    // 直接从用户对象获取姓名
    return `${user.first_name}${user.last_name ? ' ' + user.last_name : ''}`
  } else if (user?.username) {
    // 使用用户名
    return user.username
  } else if (user?.phone_number) {
    // 使用手机号
    return user.phone_number
  }

  return 'TeleSeeker User'
})

const userEmail = computed(() => {
  const user = authStore.currentUser
  return user?.phone_number || user?.username || '<EMAIL>'
})

// 生成用户头像URL
const userAvatar = computed(() => {
  const user = authStore.currentUser
  console.log('🖼️ 生成用户头像，用户数据:', user)

  // 优先使用后端返回的头像URL
  if (user?.avatar_url) {
    console.log('✅ 使用后端返回的头像URL:', user.avatar_url)
    return user.avatar_url
  }

  // 备选方案：使用默认头像服务
  const name = userName.value
  const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=128&background=3b82f6&color=ffffff&bold=true`
  console.log('🔄 使用默认头像URL:', avatarUrl)
  return avatarUrl
})

// 聊天列表相关计算属性
const groupChats = computed(() => {
  return chats.value.filter(chat => chat.type === 'group' || chat.type === 'supergroup')
    .sort((a, b) => {
      // 置顶聊天排在前面
      if (a.is_pinned && !b.is_pinned) return -1
      if (!a.is_pinned && b.is_pinned) return 1
      // 按最后消息时间排序
      const aTime = a.last_message_date ? new Date(a.last_message_date).getTime() : 0
      const bTime = b.last_message_date ? new Date(b.last_message_date).getTime() : 0
      return bTime - aTime
    })
})

const channelChats = computed(() => {
  return chats.value.filter(chat => chat.type === 'channel')
    .sort((a, b) => {
      if (a.is_pinned && !b.is_pinned) return -1
      if (!a.is_pinned && b.is_pinned) return 1
      const aTime = a.last_message_date ? new Date(a.last_message_date).getTime() : 0
      const bTime = b.last_message_date ? new Date(b.last_message_date).getTime() : 0
      return bTime - aTime
    })
})

const contactChats = computed(() => {
  return chats.value.filter(chat => chat.type === 'private')
    .sort((a, b) => {
      if (a.is_pinned && !b.is_pinned) return -1
      if (!a.is_pinned && b.is_pinned) return 1
      const aTime = a.last_message_date ? new Date(a.last_message_date).getTime() : 0
      const bTime = b.last_message_date ? new Date(b.last_message_date).getTime() : 0
      return bTime - aTime
    })
})

const botChats = computed(() => {
  return chats.value.filter(chat => chat.type === 'bot')
    .sort((a, b) => {
      if (a.is_pinned && !b.is_pinned) return -1
      if (!a.is_pinned && b.is_pinned) return 1
      const aTime = a.last_message_date ? new Date(a.last_message_date).getTime() : 0
      const bTime = b.last_message_date ? new Date(b.last_message_date).getTime() : 0
      return bTime - aTime
    })
})

// 统计数量
const groupsCount = computed(() => groupChats.value.length)
const channelsCount = computed(() => channelChats.value.length)
const contactsCount = computed(() => contactChats.value.length)
const botsCount = computed(() => botChats.value.length)

// Methods
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({ name: 'search', query: { q: searchQuery.value } })
  }
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  showUserMenu.value = false
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
  showNotifications.value = false
}

const markAllAsRead = () => {
  notificationCount.value = 0
}

const goToProfile = () => {
  router.push('/profile')
  showUserMenu.value = false
}

const goToSettings = () => {
  router.push('/settings')
  showUserMenu.value = false
}

const logout = async () => {
  try {
    console.log('🚪 用户退出登录...')
    showUserMenu.value = false

    // 使用认证store的退出方法
    await authStore.logout()

    console.log('✅ 退出成功，跳转到登录页')
    await router.push('/login')
  } catch (error) {
    console.error('❌ 退出失败:', error)
    // 即使退出失败，也强制跳转到登录页
    await router.push('/login')
  }
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}h ago`

  const days = Math.floor(hours / 24)
  return `${days}d ago`
}

// 聊天列表相关方法
const toggleChatsList = async () => {
  showChatsList.value = !showChatsList.value

  // 如果展开聊天列表且还没有加载数据，则加载聊天列表
  if (showChatsList.value && chats.value.length === 0) {
    await loadChats()
  }
}

const toggleCategory = (category: string) => {
  expandedCategories.value[category] = !expandedCategories.value[category]

  // 如果展开分类且还没有加载数据，则加载聊天列表
  if (expandedCategories.value[category] && chats.value.length === 0) {
    loadChats()
  }
}

const loadChats = async () => {
  try {
    console.log('📡 开始加载聊天列表...')
    isLoadingChats.value = true

    // 获取session_id
    const sessionId = authStore.sessionInfo?.session_id
    console.log('🔑 使用session_id:', sessionId ? sessionId.substring(0, 20) + '...' : 'null')

    // 使用演示数据进行测试
    const demoChats = [
      {
        id: 1001,
        title: "LINUX DO Channel",
        type: "channel",
        username: "linuxdo",
        participants_count: 5000,
        description: "Linux技术交流频道",
        is_verified: true,
        is_scam: false,
        is_fake: false,
        unread_count: 5,
        is_pinned: true,
        last_message_date: "2024-01-15T10:30:00Z",
        avatar_url: null
      },
      {
        id: 1002,
        title: "科技圈茶馆",
        type: "supergroup",
        username: "tech_chat",
        participants_count: 1200,
        description: "科技爱好者交流群",
        is_verified: false,
        is_scam: false,
        is_fake: false,
        unread_count: 12,
        is_pinned: false,
        last_message_date: "2024-01-15T09:45:00Z",
        avatar_url: null
      },
      {
        id: 1003,
        title: "张三",
        type: "private",
        username: "zhangsan",
        participants_count: null,
        description: null,
        is_verified: false,
        is_scam: false,
        is_fake: false,
        unread_count: 2,
        is_pinned: false,
        last_message_date: "2024-01-15T08:20:00Z",
        avatar_url: null
      },
      {
        id: 1004,
        title: "ChatGPT Bot",
        type: "bot",
        username: "chatgpt_bot",
        participants_count: null,
        description: "AI聊天机器人",
        is_verified: true,
        is_scam: false,
        is_fake: false,
        unread_count: 0,
        is_pinned: false,
        last_message_date: "2024-01-14T20:15:00Z",
        avatar_url: null
      },
      {
        id: 1005,
        title: "Quantumult X",
        type: "supergroup",
        username: "quantumultx",
        participants_count: 800,
        description: "代理工具交流群",
        is_verified: false,
        is_scam: false,
        is_fake: false,
        unread_count: 3,
        is_pinned: true,
        last_message_date: "2024-01-15T07:30:00Z",
        avatar_url: null
      },
      {
        id: 1006,
        title: "李四",
        type: "private",
        username: "lisi",
        participants_count: null,
        description: null,
        is_verified: false,
        is_scam: false,
        is_fake: false,
        unread_count: 1,
        is_pinned: false,
        last_message_date: "2024-01-14T22:10:00Z",
        avatar_url: null
      },
      {
        id: 1007,
        title: "BotFather",
        type: "bot",
        username: "botfather",
        participants_count: null,
        description: "Telegram官方机器人",
        is_verified: true,
        is_scam: false,
        is_fake: false,
        unread_count: 0,
        is_pinned: false,
        last_message_date: "2024-01-13T15:20:00Z",
        avatar_url: null
      }
    ]

    const response = { success: true, data: { chats: demoChats, is_demo: true } }
    console.log('📊 使用演示聊天数据:', response)

    if (response && response.success && response.data && response.data.chats) {
      chats.value = response.data.chats
      console.log(`✅ 成功加载 ${chats.value.length} 个聊天`)
      console.log('📋 聊天数据示例:', chats.value.slice(0, 3))
      console.log('📊 数据来源:', response.data.is_demo ? '演示数据' : (response.data.cached ? '缓存数据' : '实时数据'))

      // 计算未读消息总数
      unreadCount.value = chats.value.reduce((total, chat) => total + (chat.unread_count || 0), 0)

      if (response.data.sync_info) {
        console.log('📈 同步统计:', response.data.sync_info)
      }
    } else if (response && response.data && Array.isArray(response.data)) {
      chats.value = response.data
      console.log(`✅ 成功加载 ${chats.value.length} 个聊天 (兼容模式)`)
      console.log('📋 聊天数据示例:', chats.value.slice(0, 3))
      unreadCount.value = chats.value.reduce((total, chat) => total + (chat.unread_count || 0), 0)
    } else if (response && Array.isArray(response)) {
      // 直接是数组的情况
      chats.value = response
      console.log(`✅ 成功加载 ${chats.value.length} 个聊天 (直接数组模式)`)
      console.log('📋 聊天数据示例:', chats.value.slice(0, 3))
      unreadCount.value = chats.value.reduce((total, chat) => total + (chat.unread_count || 0), 0)
    } else {
      console.warn('⚠️ 聊天列表响应格式异常:', response)
      chats.value = []
    }

    // 输出分类统计
    console.log('📊 分类统计:')
    console.log('  - 群组:', groupChats.value.length)
    console.log('  - 频道:', channelChats.value.length)
    console.log('  - 联系人:', contactChats.value.length)
    console.log('  - 机器人:', botChats.value.length)

    // 加载自定义分组
    await loadCustomGroups()

  } catch (error) {
    console.error('❌ 加载聊天列表失败:', error)
    chats.value = []
  } finally {
    isLoadingChats.value = false
  }
}

const loadCustomGroups = async () => {
  try {
    console.log('📁 开始加载自定义分组...')

    // 使用演示自定义分组数据
    const demoGroups = [
      {
        id: "custom_1",
        title: "工作群组",
        chat_count: 5,
        created_at: "2024-01-01T00:00:00Z"
      },
      {
        id: "custom_2",
        title: "朋友圈",
        chat_count: 8,
        created_at: "2024-01-02T00:00:00Z"
      }
    ]

    customGroups.value = demoGroups
    console.log(`✅ 成功加载 ${customGroups.value.length} 个自定义分组`)

  } catch (error) {
    console.error('❌ 加载自定义分组失败:', error)
    customGroups.value = []
  }
}



const selectChat = (chat: any) => {
  selectedChatId.value = chat.id
  selectedCustomGroupId.value = null
  console.log('📱 选中聊天:', chat.title)

  // 可以在这里添加导航到聊天详情页的逻辑
  // router.push(`/chats/${chat.id}`)
}

const selectCustomGroup = (group: any) => {
  selectedCustomGroupId.value = group.id
  selectedChatId.value = null
  console.log('📁 选中自定义分组:', group.title)

  // 可以在这里添加导航到分组详情页的逻辑
  // router.push(`/groups/${group.id}`)
}

const getChatTypeClass = (type: string) => {
  const typeClasses = {
    'private': 'avatar-private',
    'group': 'avatar-group',
    'supergroup': 'avatar-group',
    'channel': 'avatar-channel',
    'bot': 'avatar-bot'
  }
  return typeClasses[type] || 'avatar-default'
}

const getChatInitial = (title: string) => {
  if (!title) return '?'
  return title.charAt(0).toUpperCase()
}

const getChatTypeLabel = (type: string) => {
  const typeLabels = {
    'private': '私聊',
    'group': '群组',
    'supergroup': '超级群组',
    'channel': '频道',
    'bot': '机器人'
  }
  return typeLabels[type] || type
}

const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// Lifecycle
onMounted(async () => {
  console.log('🎨 MainLayout 组件挂载')
  console.log('👤 当前认证状态:', authStore.isAuthenticated)
  console.log('👤 当前用户数据:', authStore.currentUser)

  // 自动加载聊天列表数据
  if (authStore.isAuthenticated) {
    console.log('🔄 用户已认证，预加载聊天列表...')
    try {
      await loadChats()
      console.log('✅ 聊天列表预加载完成')
    } catch (error) {
      console.error('❌ 聊天列表预加载失败:', error)
    }
  }

  // 点击外部关闭下拉菜单
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    if (!target.closest('.notification-icon') && !target.closest('.notification-panel')) {
      showNotifications.value = false
    }
    if (!target.closest('.user-menu') && !target.closest('.user-menu-dropdown')) {
      showUserMenu.value = false
    }
  })
})
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 左侧导航栏 */
.sidebar {
  width: 280px; /* 加宽以容纳更多内容 */
  background: #1f2937;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #374151;
}

.logo h2 {
  color: #3b82f6;
  font-size: 20px;
  font-weight: 600;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 30px;
}

.nav-section-title {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 10px;
  padding: 0 20px;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: 2px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s;
  position: relative;
}

.nav-link:hover {
  background: #374151;
  color: white;
}

.nav-item.active .nav-link {
  background: #3b82f6;
  color: white;
}

.nav-link i {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  font-size: 16px;
}

.badge {
  background: #ef4444;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
}

.expand-icon {
  margin-left: auto;
  font-size: 10px;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 聊天分类容器 */
.chat-categories-container {
  background: #374151;
  border-radius: 8px;
  margin: 8px 12px;
  padding: 8px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 聊天分类项 */
.chat-category-item {
  margin-bottom: 8px;
}

.chat-category-item:last-child {
  margin-bottom: 0;
}

/* 分类头部 */
.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #4b5563;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  font-weight: 500;
  color: #e5e7eb;
}

.category-header:hover {
  background: #6b7280;
}

.category-header .category-icon {
  font-size: 14px;
}

.category-header .category-label {
  flex: 1;
}

.category-header .category-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  min-width: 18px;
  text-align: center;
}

.category-header .expand-icon {
  font-size: 10px;
  transition: transform 0.2s;
}

.category-header .expand-icon.expanded {
  transform: rotate(180deg);
}

/* 分类聊天列表 */
.category-chats {
  margin-top: 4px;
  padding-left: 8px;
  max-height: 30vh;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #9ca3af;
  font-size: 12px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #4b5563;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 聊天分组 */
.chat-group {
  margin-bottom: 12px;
}

.chat-group-title {
  font-size: 10px;
  color: #9ca3af;
  font-weight: 600;
  margin-bottom: 6px;
  padding: 0 4px;
}

/* 聊天项 */
.chat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 2px;
  background: #374151;
}

.chat-item:hover {
  background: #4b5563;
}

.chat-item.active {
  background: #3b82f6;
}

/* 聊天头像 */
.chat-avatar {
  position: relative;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  border-radius: 50%;
}

.avatar-private { background: #8b5cf6; }
.avatar-group { background: #22c55e; }
.avatar-channel { background: #3b82f6; }
.avatar-bot { background: #f59e0b; }
.avatar-custom { background: #8b5cf6; }
.avatar-default { background: #6b7280; }

.unread-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ef4444;
  color: white;
  font-size: 8px;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 12px;
  text-align: center;
  line-height: 1;
}

/* 聊天信息 */
.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 12px;
  font-weight: 500;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.chat-members,
.chat-status {
  font-size: 10px;
  color: #9ca3af;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  margin-left: 280px; /* 对应加宽的侧边栏 */
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.top-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 50;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin-top: 2px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  font-size: 16px;
}

.search-box input {
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  width: 300px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.notification-icon {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.notification-icon:hover {
  background: #f3f4f6;
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-menu:hover {
  background: #f3f4f6;
}

.user-avatar {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  transition: opacity 0.2s;
}

.user-avatar img:hover {
  opacity: 0.8;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.user-email {
  font-size: 12px;
  color: #6b7280;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 通知面板和用户菜单下拉 */
.notification-panel,
.user-menu-dropdown {
  position: fixed;
  top: 70px;
  right: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
}

.notification-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.notification-item:last-child {
  border-bottom: none;
}

.user-menu-dropdown ul {
  list-style: none;
  padding: 8px 0;
}

.user-menu-dropdown li {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-menu-dropdown li:hover {
  background: #f3f4f6;
}

.user-menu-dropdown .divider {
  height: 1px;
  background: #e5e7eb;
  margin: 4px 0;
  padding: 0;
}

.user-menu-dropdown .divider:hover {
  background: #e5e7eb;
}

/* 图标样式 */
.icon-dashboard::before { content: "📊"; }
.icon-chat::before { content: "💬"; }
.icon-search::before { content: "🔍"; }
.icon-analytics::before { content: "📈"; }
.icon-bell::before { content: "🔔"; }
.icon-settings::before { content: "⚙️"; }
.icon-chevron-down::before { content: "▼"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .search-box input {
    width: 200px;
  }
}
</style>
