/**
 * API客户端工具类
 */
import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// API配置
export const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? ''
  : 'http://localhost:8000'
const API_VERSION = 'v1'

// 请求/响应类型定义
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  total?: number
}

export interface PaginationParams {
  page?: number
  page_size?: number
  skip?: number
  limit?: number
}

export interface SearchParams extends PaginationParams {
  query?: string
  mode?: string
  chat_id?: number
  message_type?: string
  start_date?: string
  end_date?: string
  sender?: string
  sort_by?: string
  match_mode?: string
  filters?: string[]
}

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

class ApiClient {
  private instance: AxiosInstance
  private token: string | null = null

  constructor() {
    this.instance = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
    this.loadTokenFromStorage()
  }

  /**
   * 设置请求/响应拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`
        }

        // 添加会话ID（如果存在）
        const sessionId = this.getSessionId()
        if (sessionId) {
          config.params = {
            ...config.params,
            session_id: sessionId,
          }
        }

        console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`, config.params || config.data)
        return config
      },
      (error) => {
        console.error('🚫 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
        return response
      },
      (error) => {
        console.error('🚫 响应错误:', error.response?.data || error.message)
        
        // 处理常见错误
        if (error.response) {
          const status = error.response.status
          const message = error.response.data?.detail || error.response.data?.message || '请求失败'

          switch (status) {
            case HTTP_STATUS.UNAUTHORIZED:
              this.handleUnauthorized()
              ElMessage.error('认证失败，请重新登录')
              break
            case HTTP_STATUS.FORBIDDEN:
              ElMessage.error('权限不足')
              break
            case HTTP_STATUS.NOT_FOUND:
              ElMessage.error('请求的资源不存在')
              break
            case HTTP_STATUS.INTERNAL_SERVER_ERROR:
              ElMessage.error('服务器内部错误')
              break
            default:
              ElMessage.error(message)
          }
        } else if (error.request) {
          ElMessage.error('网络连接失败，请检查网络')
        } else {
          ElMessage.error('请求配置错误')
        }

        return Promise.reject(error)
      }
    )
  }

  /**
   * 从本地存储加载token
   */
  private loadTokenFromStorage() {
    this.token = localStorage.getItem('access_token')
  }

  /**
   * 设置认证token
   */
  public setToken(token: string) {
    this.token = token
    localStorage.setItem('access_token', token)
  }

  /**
   * 清除认证token
   */
  public clearToken() {
    this.token = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('session_id')
  }

  /**
   * 获取会话ID
   */
  private getSessionId(): string | null {
    return localStorage.getItem('session_id')
  }

  /**
   * 设置会话ID
   */
  public setSessionId(sessionId: string) {
    localStorage.setItem('session_id', sessionId)
  }

  /**
   * 处理未授权错误
   */
  private handleUnauthorized() {
    this.clearToken()
    // 可以触发路由跳转到登录页
    window.location.href = '/auth'
  }

  /**
   * 通用请求方法
   */
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.request<ApiResponse<T>>(config)
      return (response.data.data ?? response.data) as T
    } catch (error) {
      throw error
    }
  }

  /**
   * GET请求
   */
  public async get<T>(url: string, params?: any): Promise<T> {
    return this.request<T>({ method: 'GET', url, params })
  }

  /**
   * POST请求
   */
  public async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>({ method: 'POST', url, data })
  }

  /**
   * PUT请求
   */
  public async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>({ method: 'PUT', url, data })
  }

  /**
   * DELETE请求
   */
  public async delete<T>(url: string): Promise<T> {
    return this.request<T>({ method: 'DELETE', url })
  }

  // === 认证相关API ===

  /**
   * 请求Telegram验证码
   */
  public async requestTelegramCode(phoneNumber: string): Promise<any> {
    return this.post('/auth/telegram/request-code', {
      phone_number: phoneNumber,
    })
  }

  /**
   * 验证Telegram验证码
   */
  public async verifyTelegramCode(phoneNumber: string, code: string): Promise<any> {
    return this.post('/auth/telegram/verify-code', {
      phone_number: phoneNumber,
      code,
    })
  }

  /**
   * 获取会话信息
   */
  public async getSessionInfo(sessionId: string): Promise<any> {
    return this.get('/auth/session', { session_id: sessionId })
  }

  /**
   * 获取Telegram连接状态
   */
  public async getTelegramConnectionStatus(sessionId: string): Promise<any> {
    return this.get('/auth/telegram/connection-status', { session_id: sessionId })
  }

  /**
   * 获取Telegram群组频道列表
   */
  public async getTelegramChats(sessionId?: string): Promise<any> {
    // 使用新的统一API，支持session_id参数
    if (sessionId) {
      return this.get(`/telegram-updates/chats?session_id=${sessionId}`)
    } else {
      return this.get('/telegram-updates/chats')
    }
  }

  /**
   * 获取聊天预览信息
   */
  public async getChatPreview(chatId: number, sessionId: string): Promise<any> {
    // 使用telegram-updates API获取聊天预览
    return this.get(`/telegram-updates/chat/${sessionId}/${chatId}`)
  }

  /**
   * 获取演示聊天列表
   */
  public async getDemoChats(): Promise<any> {
    return this.get('/telegram-updates/demo/chats')
  }

  /**
   * 获取telegram-updates统计信息
   */
  public async getTelegramUpdatesStats(): Promise<any> {
    return this.get('/telegram-updates/stats')
  }

  /**
   * 刷新聊天缓存
   */
  public async refreshChatsCache(userId: string): Promise<any> {
    return this.post(`/telegram-updates/chats/${userId}/refresh`)
  }

  /**
   * 获取聊天历史记录
   */
  public async getChatHistory(userId: string, chatId: number, limit: number = 50): Promise<any> {
    return this.get(`/telegram-updates/history/${userId}/${chatId}`, { limit })
  }

  /**
   * 用户登录到telegram-updates
   */
  public async telegramUpdatesLogin(phoneNumber: string, apiId?: number, apiHash?: string): Promise<any> {
    return this.post('/telegram-updates/auth/login', {
      phone_number: phoneNumber,
      api_id: apiId,
      api_hash: apiHash
    })
  }

  /**
   * 验证telegram-updates验证码
   */
  public async telegramUpdatesVerifyCode(userId: string, code: string, phoneCodeHash: string, password?: string): Promise<any> {
    return this.post('/telegram-updates/auth/verify', {
      user_id: userId,
      code,
      phone_code_hash: phoneCodeHash,
      password
    })
  }

  /**
   * 获取用户状态
   */
  public async getTelegramUserStatus(userId: string): Promise<any> {
    return this.get(`/telegram-updates/status/${userId}`)
  }

  /**
   * 用户登出
   */
  public async telegramUpdatesLogout(userId: string): Promise<any> {
    return this.post(`/telegram-updates/logout/${userId}`)
  }

  // === 搜索相关API ===

  /**
   * 搜索消息
   */
  public async searchMessages(params: SearchParams): Promise<any> {
    return this.post('/search/messages', params)
  }

  /**
   * 获取搜索建议
   */
  public async getSearchSuggestions(query: string): Promise<string[]> {
    return this.get('/search/suggestions', { query })
  }

  /**
   * AI问答
   */
  public async askQuestion(question: string, context?: any): Promise<any> {
    return this.post('/ai/chat', { question, context })
  }

  // === Telegram相关API ===

  /**
   * 获取聊天列表
   */
  public async getChats(params?: PaginationParams & { session_id?: string }): Promise<any> {
    return this.get('/telegram/chats', params)
  }

  /**
   * 获取聊天详情
   */
  public async getChatDetail(chatId: number): Promise<any> {
    return this.get(`/telegram/chats/${chatId}`)
  }

  /**
   * 获取消息列表
   */
  public async getMessages(chatId: number, params?: PaginationParams): Promise<any> {
    return this.get(`/telegram/chats/${chatId}/messages`, params)
  }

  /**
   * 获取用户信息
   */
  public async getUserInfo(userId: number): Promise<any> {
    return this.get(`/telegram/users/${userId}`)
  }

  // === 同步相关API ===

  /**
   * 开始数据同步
   */
  public async startSync(sessionId: string, config: any): Promise<any> {
    return this.post('/sync/start', { ...config, session_id: sessionId })
  }

  /**
   * 获取同步状态
   */
  public async getSyncStatus(taskId: string): Promise<any> {
    return this.get(`/sync/status/${taskId}`)
  }

  /**
   * 停止同步任务
   */
  public async stopSync(taskId: string): Promise<any> {
    return this.post(`/sync/stop/${taskId}`)
  }

  /**
   * 获取同步历史
   */
  public async getSyncHistory(params?: PaginationParams): Promise<any> {
    return this.get('/sync/history', params)
  }

  /**
   * 获取可同步的聊天
   */
  public async getAvailableChats(sessionId: string): Promise<any> {
    return this.get('/sync/chats/available', { session_id: sessionId })
  }

  // === 分析相关API ===

  /**
   * 获取系统概览
   */
  public async getSystemOverview(): Promise<any> {
    return this.get('/analysis/overview')
  }

  /**
   * 获取聊天活跃度分析
   */
  public async getChatActivityAnalysis(chatId: number, days: number = 30): Promise<any> {
    return this.get(`/analysis/chats/${chatId}/activity`, { days })
  }

  /**
   * 获取用户行为分析
   */
  public async getUserBehaviorAnalysis(userId: number, days: number = 30): Promise<any> {
    return this.get(`/analysis/users/${userId}/behavior`, { days })
  }

  /**
   * 获取消息趋势
   */
  public async getMessageTrends(params: any): Promise<any> {
    return this.get('/analysis/trends/messages', params)
  }

  /**
   * 获取内容分析
   */
  public async getContentAnalysis(params: any): Promise<any> {
    return this.get('/analysis/content/analysis', params)
  }

  // === WebSocket连接 ===

  /**
   * 创建WebSocket连接
   */
  public createWebSocket(sessionId: string): WebSocket {
    const wsUrl = `${API_BASE_URL.replace('http', 'ws')}/api/${API_VERSION}/ws/connect/${sessionId}`
    console.log('🔌 连接WebSocket:', wsUrl)
    return new WebSocket(wsUrl)
  }
}

// 创建单例实例
export const apiClient = new ApiClient()

// 导出类型
export type { ApiClient }
export default apiClient 