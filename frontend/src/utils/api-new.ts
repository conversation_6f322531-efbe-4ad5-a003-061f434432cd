/**
 * 现代化API客户端 - 基于README.md规范
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// 配置
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
const TIMEOUT = 30000

class ApiClient {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      baseURL: BASE_URL,
      timeout: TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证令牌
        const token = localStorage.getItem('teleseeker_access_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('[API] Request error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response
      },
      (error) => {
        console.error('[API] Response error:', error.response?.data || error.message)

        // 处理认证错误
        if (error.response?.status === 401) {
          localStorage.removeItem('teleseeker_access_token')
          localStorage.removeItem('teleseeker_user')
          window.location.href = '/login'
        }

        return Promise.reject(error)
      }
    )
  }

  // 基础HTTP方法
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.get(url, config)
  }

  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.post(url, data, config)
  }

  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.put(url, data, config)
  }

  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.delete(url, config)
  }

  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.patch(url, data, config)
  }

  // ==================== 认证相关 API ====================
  
  public async requestCode(phoneNumber: string): Promise<any> {
    return this.post('/auth/request_code', { phone_number: phoneNumber })
  }

  public async loginPhone(phoneNumber: string, phoneCodeHash: string, code: string): Promise<any> {
    return this.post('/auth/login_phone', {
      phone_number: phoneNumber,
      phone_code_hash: phoneCodeHash,
      code: code
    })
  }

  public async loginPassword(password: string): Promise<any> {
    return this.post('/auth/login_password', { password })
  }

  public async loginApiId(apiId: number, apiHash: string): Promise<any> {
    return this.post('/auth/login_api_id', {
      api_id: apiId,
      api_hash: apiHash
    })
  }

  public async logout(): Promise<any> {
    return this.post('/auth/logout')
  }

  public async getMe(): Promise<any> {
    return this.get('/auth/me')
  }

  // ==================== 聊天相关 API ====================

  public async getChats(): Promise<any> {
    console.log('📡 API: 获取聊天列表...')
    const response = await this.get('/chats')
    console.log('📊 API: 聊天列表响应:', response)
    return response
  }

  public async getChatMessages(
    telegramChatId: number,
    limit: number = 50,
    offset: number = 0,
    beforeMessageId?: number,
    afterMessageId?: number
  ): Promise<any> {
    const params: any = { limit, offset }
    if (beforeMessageId) params.before_message_id = beforeMessageId
    if (afterMessageId) params.after_message_id = afterMessageId

    return this.get(`/chats/${telegramChatId}/messages`, { params })
  }

  public async sendMessage(telegramChatId: number, text: string): Promise<any> {
    return this.post(`/chats/${telegramChatId}/messages`, { text })
  }

  public async downloadHistory(telegramChatId: number, fromMessageId?: number, fromDate?: string): Promise<any> {
    return this.post(`/chats/${telegramChatId}/download`, {
      from_message_id: fromMessageId,
      from_date: fromDate
    })
  }

  // ==================== 搜索相关 API ====================

  public async searchMessages(query: string, options?: {
    chat_telegram_ids?: number[]
    sender_telegram_ids?: number[]
    start_date?: string
    end_date?: string
    search_type?: 'keyword' | 'semantic'
    limit?: number
    offset?: number
  }): Promise<any> {
    return this.post('/search/messages', { query, ...options })
  }

  // ==================== 概览相关 API ====================

  public async getOverview(): Promise<any> {
    return this.get('/overview')
  }

  public async generateSummary(options: {
    message_ids?: number[]
    recent_n_messages?: number
    date_range?: { start: string; end: string }
  }): Promise<any> {
    return this.post('/overview/summarize', options)
  }

  // ==================== 设置相关 API ====================

  public async getSettings(): Promise<any> {
    return this.get('/settings')
  }

  public async updateSetting(key: string, value: any): Promise<any> {
    return this.post('/settings', { setting_key: key, setting_value: value })
  }

  // ==================== 关键词提醒相关 API ====================

  public async getKeywordAlerts(): Promise<any> {
    return this.get('/keyword-alerts')
  }

  public async createKeywordAlert(keyword: string, chatId?: number): Promise<any> {
    return this.post('/keyword-alerts', { keyword, chat_id: chatId })
  }

  public async deleteKeywordAlert(alertId: number): Promise<any> {
    return this.delete(`/keyword-alerts/${alertId}`)
  }

  public async toggleKeywordAlert(alertId: number, isActive: boolean): Promise<any> {
    return this.patch(`/keyword-alerts/${alertId}`, { is_active: isActive })
  }

  // ==================== 任务相关 API ====================

  public async getTaskStatus(taskId: string): Promise<any> {
    return this.get(`/tasks/${taskId}`)
  }

  public async cancelTask(taskId: string): Promise<any> {
    return this.post(`/tasks/${taskId}/cancel`)
  }
}

// 创建全局实例
export const apiClient = new ApiClient()
export default apiClient
