"""
TeleSeeker 后端主入口文件
基于README.md文档规范
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

try:
    from app.api.routes_minimal import router
    print("✅ Successfully imported minimal routes")

    # 尝试导入配置和数据库
    try:
        from app.core.config import settings
        from app.db.base import init_database, close_database
        print("✅ Successfully imported config and database")
    except ImportError as e:
        print(f"⚠️ Config/Database import error: {e}")
        # 创建默认配置
        class DefaultSettings:
            PROJECT_NAME = "TeleSeeker"
            APP_VERSION = "1.0.0"
            API_V1_STR = "/api/v1"
            DEBUG = True
            HOST = "0.0.0.0"
            PORT = 8000
            LOG_LEVEL = "INFO"
            BACKEND_CORS_ORIGINS = ["http://localhost:5173", "http://127.0.0.1:5173"]

        settings = DefaultSettings()

        # 创建空的数据库函数
        async def init_database():
            print("⚠️ Database initialization skipped")
            pass

        async def close_database():
            print("⚠️ Database cleanup skipped")
            pass

except ImportError as e:
    print(f"❌ Critical import error: {e}")
    print("Creating emergency minimal router...")

    # 创建紧急最小路由器
    from fastapi import APIRouter
    router = APIRouter()

    @router.get("/test")
    async def test():
        return {"message": "Emergency API is working!"}

    @router.get("/health")
    async def health():
        return {"status": "emergency", "message": "Running with minimal functionality"}

    # 创建默认配置
    class DefaultSettings:
        PROJECT_NAME = "TeleSeeker"
        APP_VERSION = "1.0.0"
        API_V1_STR = "/api/v1"
        DEBUG = True
        HOST = "0.0.0.0"
        PORT = 8000
        LOG_LEVEL = "INFO"
        BACKEND_CORS_ORIGINS = ["http://localhost:5173", "http://127.0.0.1:5173"]

    settings = DefaultSettings()

    # 创建空的数据库函数
    async def init_database():
        print("⚠️ Database initialization skipped (emergency mode)")
        pass

    async def close_database():
        print("⚠️ Database cleanup skipped (emergency mode)")
        pass


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    print("🚀 Starting TeleSeeker backend...")
    try:
        await init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
    
    yield
    
    # 关闭时清理资源
    print("🛑 Shutting down TeleSeeker backend...")
    try:
        await close_database()
        print("✅ Database connections closed")
    except Exception as e:
        print(f"❌ Database cleanup failed: {e}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.APP_VERSION,
    description="商业化第三方Telegram客户端",
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Welcome to TeleSeeker API",
        "version": settings.APP_VERSION,
        "docs": "/docs" if settings.DEBUG else "Documentation disabled in production"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "TeleSeeker Backend",
        "version": settings.APP_VERSION
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
