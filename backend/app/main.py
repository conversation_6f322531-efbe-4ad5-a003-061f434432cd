"""
FastAPI 主应用文件
配置应用、中间件、路由等
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
import uvicorn
import time
import warnings
from datetime import datetime
import asyncio
import os
import glob

# 忽略Pydantic警告
warnings.filterwarnings("ignore", message="Field.*has conflict with protected namespace.*")

from .core import settings, get_logger, init_db, close_db

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("Starting TeleSeeker application...")
    
    # 检查启动模式
    fast_start = os.getenv('TELESEEKER_FAST_START')
    lazy_start = os.getenv('TELESEEKER_LAZY_START')
    
    if lazy_start:
        logger.info("🚀 Lazy start mode: deferring ALL Telegram services until first access")
        # 延迟启动模式：完全跳过所有Telegram相关的初始化
        # 只做最基本的应用初始化
        pass
    elif fast_start:
        logger.info("🚀 Ultra-fast boot mode: skipping ALL non-essential initialization")
        # 完全跳过所有清理和初始化操作
        pass
    else:
        # 完整初始化模式
        await cleanup_telegram_clients()
        await cleanup_session_locks()
        # 同步初始化数据库
        await init_db_async()
    
    logger.info("✅ Application startup completed")
    
    yield
    
    # 关闭时的清理
    logger.info("Shutting down TeleSeeker application...")
    if not lazy_start:  # 延迟启动模式下可能没有初始化Telegram服务
        await cleanup_telegram_clients()


async def init_db_async():
    """异步初始化数据库，不阻塞应用启动"""
    try:
        await init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.warning(f"Database initialization failed: {e}")
        logger.info("Application will continue without database connection")


async def cleanup_telegram_clients():
    """清理Telegram客户端连接"""
    try:
        from app.services.telegram_auth import telegram_auth_service
        await telegram_auth_service.cleanup_all_clients()
        logger.info("✅ Telegram客户端清理完成")
    except Exception as e:
        logger.warning(f"⚠️ 清理Telegram客户端时出错: {e}")


async def cleanup_session_locks():
    """清理可能被锁定的session文件"""
    try:
        logger.info("🧹 清理session文件锁定...")
        
        # 获取session目录
        sessions_dir = "sessions"
        if not os.path.exists(sessions_dir):
            return
            
        # 查找所有journal文件（SQLite锁定标志）
        journal_files = glob.glob(os.path.join(sessions_dir, "*.session-journal"))
        
        if journal_files:
            logger.info(f"🔍 发现 {len(journal_files)} 个session锁定文件")
            for journal_file in journal_files:
                try:
                    # 删除journal文件来释放锁定
                    os.remove(journal_file)
                    logger.debug(f"🗑️ 删除journal文件: {journal_file}")
                except Exception as e:
                    logger.debug(f"⚠️ 无法删除journal文件 {journal_file}: {e}")
        
        # 创建认证服务实例并清理所有客户端
        from app.services.telegram_auth import TelegramAuthService
        auth_service = TelegramAuthService()
        await auth_service.cleanup_all_clients()
        
        logger.info("✅ Session文件锁定清理完成")
        
    except Exception as e:
        logger.warning(f"⚠️ 清理session锁定时出错: {e}")


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""
    
    # 快速启动模式：最小配置
    fast_start = os.getenv('TELESEEKER_FAST_START')
    lazy_start = os.getenv('TELESEEKER_LAZY_START')
    
    if lazy_start:
        # 延迟启动模式：极简配置
        app = FastAPI(
            title="TeleSeeker",
            version="1.0.0",
            lifespan=lifespan,
            debug=False,  # 禁用调试模式
            docs_url=None,  # 禁用文档
            redoc_url=None,  # 禁用redoc
            openapi_url=None  # 禁用OpenAPI
        )
        
        # 最简CORS配置
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )
    elif fast_start:
        # 超快启动：最小配置
        app = FastAPI(
            title=settings.PROJECT_NAME,
            version=settings.APP_VERSION,
            lifespan=lifespan,
            debug=False,  # 禁用调试模式
            docs_url=None,  # 禁用文档
            redoc_url=None,  # 禁用redoc
            openapi_url=None  # 禁用OpenAPI
        )
        
        # 最简CORS配置
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )
    else:
        # 完整配置
        app = FastAPI(
            title=settings.PROJECT_NAME,
            version=settings.APP_VERSION,
            description="一个强大、可扩展、智能化的Telegram聊天记录搜索引擎和分析平台",
            openapi_url=f"{settings.API_V1_STR}/openapi.json",
            docs_url=f"{settings.API_V1_STR}/docs",
            redoc_url=f"{settings.API_V1_STR}/redoc",
            lifespan=lifespan,
            debug=settings.DEBUG,
        )
        
        # 完整CORS配置
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"] if settings.DEBUG else [str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
            allow_headers=["*"],
            expose_headers=["*"]
        )
        
        # 受信任主机中间件（生产环境）
        if not settings.DEBUG:
            app.add_middleware(
                TrustedHostMiddleware,
                allowed_hosts=["localhost", "127.0.0.1", settings.HOST]
            )
    
    # 注册异常处理器（延迟启动和快速启动模式下跳过）
    if not (fast_start or lazy_start):
        @app.exception_handler(RequestValidationError)
        async def validation_exception_handler(request, exc):
            logger.warning(f"Validation error: {exc}")
            return JSONResponse(
                status_code=422,
                content={
                    "detail": "请求数据验证失败",
                    "errors": exc.errors()
                }
            )
        
        @app.exception_handler(500)
        async def internal_server_error_handler(request, exc):
            logger.error(f"Internal server error: {exc}")
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "服务器内部错误",
                    "error": str(exc) if settings.DEBUG else "Internal server error"
                }
            )
    
    # 添加中间件记录请求日志（根据模式决定详细程度）
    if settings.DEBUG and not (fast_start or lazy_start):
        # 完整调试模式
        @app.middleware("http")
        async def log_requests(request, call_next):
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # 只记录非健康检查的请求
            if not request.url.path.endswith("/health"):
                logger.info(
                    f"{request.method} {request.url} - "
                    f"Status: {response.status_code} - "
                    f"Time: {process_time:.4f}s"
                )
            return response
    elif settings.DEBUG and fast_start:
        # 快速启动模式：只记录错误
        @app.middleware("http")
        async def simple_log_requests(request, call_next):
            response = await call_next(request)
            # 只记录错误请求
            if response.status_code >= 400:
                logger.warning(f"{request.method} {request.url} - Status: {response.status_code}")
            return response
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        if fast_start:
            return {"status": "healthy"}
        else:
            return {
                "status": "healthy",
                "version": settings.APP_VERSION,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径信息"""
        return {
            "message": f"欢迎使用 {settings.PROJECT_NAME}",
            "version": settings.APP_VERSION,
            "docs": f"{settings.API_V1_STR}/docs",
            "openapi": f"{settings.API_V1_STR}/openapi.json"
        }
    
    # 注册API路由
    from .api import api_router
    app.include_router(api_router, prefix=settings.API_V1_STR)
    
    # 添加静态文件支持（用于演示页面和头像）

    # 添加头像静态文件服务
    # 获取正确的头像目录路径
    avatar_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "storage", "avatars"))

    print(f"🔍 检查头像目录: {avatar_dir}")
    print(f"🔍 目录是否存在: {os.path.exists(avatar_dir)}")

    if os.path.exists(avatar_dir):
        app.mount("/api/v1/avatars", StaticFiles(directory=avatar_dir), name="avatars")
        print(f"✅ 头像静态文件服务已启用: {avatar_dir}")
        logger.info(f"✅ 头像静态文件服务已启用: {avatar_dir}")

        # 列出头像文件
        for root, dirs, files in os.walk(avatar_dir):
            for file in files:
                if file.endswith('.jpg'):
                    rel_path = os.path.relpath(os.path.join(root, file), avatar_dir)
                    print(f"📸 发现头像文件: {rel_path}")
    else:
        # 创建头像目录
        os.makedirs(avatar_dir, exist_ok=True)
        os.makedirs(os.path.join(avatar_dir, "users"), exist_ok=True)
        os.makedirs(os.path.join(avatar_dir, "chats"), exist_ok=True)
        os.makedirs(os.path.join(avatar_dir, "channels"), exist_ok=True)
        app.mount("/api/v1/avatars", StaticFiles(directory=avatar_dir), name="avatars")
        print(f"✅ 头像目录已创建并启用静态文件服务: {avatar_dir}")
        logger.info(f"✅ 头像目录已创建并启用静态文件服务: {avatar_dir}")

    # 检查前端目录是否存在
    frontend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "..", "frontend")
    if os.path.exists(frontend_dir):
        # 挂载静态文件目录
        app.mount("/static", StaticFiles(directory=frontend_dir), name="static")

        # 提供演示页面
        @app.get("/telegram-updates-demo.html")
        async def serve_demo_page():
            """提供 Telegram Updates 演示页面"""
            demo_file = os.path.join(frontend_dir, "telegram-updates-demo.html")
            if os.path.exists(demo_file):
                return FileResponse(demo_file)
            else:
                return {"error": "演示页面未找到"}
    
    return app


# 创建应用实例
app = create_application()

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=False,  # 禁用自动重载以减少问题
        log_level=settings.LOG_LEVEL.lower(),
        access_log=False,  # 禁用访问日志以减少噪音
    ) 