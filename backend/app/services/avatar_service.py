"""
头像服务模块
负责下载、存储和管理用户、群组、频道的头像
"""

import os
import hashlib
import aiohttp
import aiofiles
from pathlib import Path
from typing import Optional, Dict, Any
from telethon import TelegramClient
from telethon.tl.types import User, Chat, Channel
import time

class AvatarService:
    """头像服务类"""
    
    def __init__(self, storage_path: str = "storage/avatars"):
        """
        初始化头像服务
        
        Args:
            storage_path: 头像存储路径
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (self.storage_path / "users").mkdir(exist_ok=True)
        (self.storage_path / "chats").mkdir(exist_ok=True)
        (self.storage_path / "channels").mkdir(exist_ok=True)
        
        print(f"📁 头像存储路径: {self.storage_path.absolute()}")
    
    def _get_avatar_filename(self, entity_id: int, entity_type: str) -> str:
        """
        生成头像文件名
        
        Args:
            entity_id: 实体ID（用户ID、群组ID等）
            entity_type: 实体类型（user、chat、channel）
            
        Returns:
            头像文件名
        """
        # 使用ID和时间戳生成唯一文件名
        timestamp = int(time.time())
        hash_str = hashlib.md5(f"{entity_id}_{timestamp}".encode()).hexdigest()[:8]
        return f"{entity_id}_{hash_str}.jpg"
    
    def _get_avatar_path(self, entity_id: int, entity_type: str) -> Path:
        """
        获取头像存储路径
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型
            
        Returns:
            头像文件路径
        """
        filename = self._get_avatar_filename(entity_id, entity_type)
        return self.storage_path / f"{entity_type}s" / filename
    
    async def download_user_avatar(self, client: TelegramClient, user: User) -> Optional[str]:
        """
        下载用户头像
        
        Args:
            client: Telegram客户端
            user: 用户对象
            
        Returns:
            头像文件路径（相对路径）
        """
        try:
            print(f"📸 开始下载用户头像: {user.first_name} (ID: {user.id})")
            
            # 检查用户是否有头像
            if not user.photo:
                print(f"❌ 用户 {user.first_name} 没有头像")
                return None
            
            # 生成头像文件路径
            avatar_path = self._get_avatar_path(user.id, "user")
            
            # 下载头像
            await client.download_profile_photo(user, file=str(avatar_path))
            
            # 返回相对路径
            relative_path = str(avatar_path.relative_to(self.storage_path.parent))
            print(f"✅ 用户头像下载成功: {relative_path}")
            
            return relative_path
            
        except Exception as e:
            print(f"❌ 下载用户头像失败: {e}")
            return None
    
    async def download_chat_avatar(self, client: TelegramClient, chat: Chat) -> Optional[str]:
        """
        下载群组头像
        
        Args:
            client: Telegram客户端
            chat: 群组对象
            
        Returns:
            头像文件路径（相对路径）
        """
        try:
            print(f"📸 开始下载群组头像: {chat.title} (ID: {chat.id})")
            
            # 检查群组是否有头像
            if not chat.photo:
                print(f"❌ 群组 {chat.title} 没有头像")
                return None
            
            # 生成头像文件路径
            avatar_path = self._get_avatar_path(chat.id, "chat")
            
            # 下载头像
            await client.download_profile_photo(chat, file=str(avatar_path))
            
            # 返回相对路径
            relative_path = str(avatar_path.relative_to(self.storage_path.parent))
            print(f"✅ 群组头像下载成功: {relative_path}")
            
            return relative_path
            
        except Exception as e:
            print(f"❌ 下载群组头像失败: {e}")
            return None
    
    async def download_channel_avatar(self, client: TelegramClient, channel: Channel) -> Optional[str]:
        """
        下载频道头像
        
        Args:
            client: Telegram客户端
            channel: 频道对象
            
        Returns:
            头像文件路径（相对路径）
        """
        try:
            print(f"📸 开始下载频道头像: {channel.title} (ID: {channel.id})")
            
            # 检查频道是否有头像
            if not channel.photo:
                print(f"❌ 频道 {channel.title} 没有头像")
                return None
            
            # 生成头像文件路径
            avatar_path = self._get_avatar_path(channel.id, "channel")
            
            # 下载头像
            await client.download_profile_photo(channel, file=str(avatar_path))
            
            # 返回相对路径
            relative_path = str(avatar_path.relative_to(self.storage_path.parent))
            print(f"✅ 频道头像下载成功: {relative_path}")
            
            return relative_path
            
        except Exception as e:
            print(f"❌ 下载频道头像失败: {e}")
            return None
    
    def get_avatar_url(self, avatar_path: Optional[str], entity_type: str, entity_id: int, entity_name: str = "") -> str:
        """
        获取头像URL
        
        Args:
            avatar_path: 头像文件路径
            entity_type: 实体类型
            entity_id: 实体ID
            entity_name: 实体名称（用于生成默认头像）
            
        Returns:
            头像URL
        """
        if avatar_path and os.path.exists(avatar_path):
            # 返回本地头像URL
            return f"/api/v1/avatars/{avatar_path.replace('storage/', '')}"
        else:
            # 返回默认头像URL
            name = entity_name or f"{entity_type}_{entity_id}"
            colors = {
                "user": "3b82f6",      # 蓝色
                "chat": "10b981",      # 绿色
                "channel": "f59e0b"    # 黄色
            }
            color = colors.get(entity_type, "6b7280")
            
            return f"https://ui-avatars.com/api/?name={entity_name}&size=128&background={color}&color=ffffff&bold=true"
    
    async def cleanup_old_avatars(self, days: int = 30):
        """
        清理旧的头像文件
        
        Args:
            days: 保留天数
        """
        try:
            print(f"🧹 开始清理 {days} 天前的头像文件...")
            
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            cleaned_count = 0
            
            for entity_type in ["users", "chats", "channels"]:
                entity_path = self.storage_path / entity_type
                if entity_path.exists():
                    for avatar_file in entity_path.glob("*.jpg"):
                        if avatar_file.stat().st_mtime < cutoff_time:
                            avatar_file.unlink()
                            cleaned_count += 1
            
            print(f"✅ 清理完成，删除了 {cleaned_count} 个旧头像文件")
            
        except Exception as e:
            print(f"❌ 清理头像文件失败: {e}")

# 全局头像服务实例
avatar_service = AvatarService()
