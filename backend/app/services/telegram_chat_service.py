"""
Telegram聊天服务
处理聊天数据的保存和获取
"""

import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from datetime import datetime, timezone
from loguru import logger

from ..db.models import Chat, User
from ..db.crud import ChatCRUD, UserCRUD


class TelegramChatService:
    """Telegram聊天数据服务"""
    
    @staticmethod
    async def get_cached_chats(session_id: str, db: AsyncSession) -> Dict[str, Any]:
        """从数据库获取缓存的聊天数据"""
        try:
            logger.info(f"🔍 查找缓存的聊天数据: {session_id[:20]}...")
            
            # 根据session_id查找用户
            user = await TelegramChatService._get_user_by_session(session_id, db)
            if not user:
                logger.warning("❌ 未找到对应的用户")
                return {
                    "success": False,
                    "error": "用户不存在",
                    "chats": [],
                    "count": 0
                }
            
            # 获取用户的聊天列表
            chats = await ChatCRUD.get_user_chats(db, user.user_id)
            
            if not chats:
                logger.info("❌ 数据库中没有缓存的聊天数据")
                return {
                    "success": False,
                    "error": "没有缓存数据",
                    "chats": [],
                    "count": 0
                }
            
            # 转换为API格式
            chat_list = []
            for chat in chats:
                chat_data = {
                    "id": chat.telegram_chat_id,
                    "title": chat.title,
                    "type": chat.type,
                    "username": None,  # 暂时不存储username
                    "participants_count": None,
                    "description": None,
                    "is_verified": False,
                    "is_scam": False,
                    "is_fake": False,
                    "unread_count": 0,
                    "is_pinned": False,
                    "last_message_date": chat.updated_at.isoformat() if chat.updated_at else None,
                    "avatar_url": None,
                    "cached": True
                }
                chat_list.append(chat_data)
            
            logger.info(f"✅ 从数据库获取到 {len(chat_list)} 个缓存聊天")
            
            return {
                "success": True,
                "chats": chat_list,
                "count": len(chat_list),
                "last_sync": max(chat.updated_at for chat in chats).isoformat() if chats else None
            }
            
        except Exception as e:
            logger.error(f"❌ 获取缓存聊天数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "chats": [],
                "count": 0
            }
    
    @staticmethod
    async def save_chats(session_id: str, chats: List[Dict[str, Any]], db: AsyncSession) -> Dict[str, Any]:
        """保存聊天数据到数据库（带重试机制）"""
        import asyncio

        max_retries = 3
        retry_delay = 1  # 秒

        for attempt in range(max_retries):
            try:
                logger.info(f"💾 开始保存 {len(chats)} 个聊天到数据库... (尝试 {attempt + 1}/{max_retries})")

                # 根据session_id查找或创建用户
                user = await TelegramChatService._get_or_create_user_by_session(session_id, db)
                if not user:
                    logger.error("❌ 无法获取或创建用户")
                    return {
                        "success": False,
                        "error": "用户创建失败",
                        "new_count": 0,
                        "updated_count": 0
                    }

                new_count = 0
                updated_count = 0

                for chat_data in chats:
                    try:
                        telegram_chat_id = chat_data.get("id")
                        title = chat_data.get("title", "未知聊天")
                        chat_type = chat_data.get("type", "unknown")

                        if not telegram_chat_id:
                            logger.warning("⚠️ 跳过无效的聊天数据（缺少ID）")
                            continue

                        # 检查聊天是否已存在
                        existing_chat = await ChatCRUD.get_chat_by_telegram_id(db, telegram_chat_id)

                        if existing_chat:
                            # 更新现有聊天
                            existing_chat.title = title
                            existing_chat.type = chat_type
                            existing_chat.updated_at = datetime.now(timezone.utc)
                            updated_count += 1
                            logger.debug(f"🔄 更新聊天: {title}")
                        else:
                            # 创建新聊天
                            new_chat = await ChatCRUD.create_chat(
                                db,
                                user_id=user.user_id,
                                telegram_chat_id=telegram_chat_id,
                                title=title,
                                chat_type=chat_type
                            )
                            new_count += 1
                            logger.debug(f"➕ 新增聊天: {title}")

                    except Exception as e:
                        logger.warning(f"⚠️ 保存单个聊天失败: {e}")
                        continue

                # 提交事务
                await db.commit()

                logger.info(f"✅ 聊天数据保存完成: 新增 {new_count} 个，更新 {updated_count} 个")

                return {
                    "success": True,
                    "new_count": new_count,
                    "updated_count": updated_count,
                    "total_processed": len(chats)
                }

            except Exception as e:
                error_msg = str(e).lower()
                if "database is locked" in error_msg or "deadlock" in error_msg:
                    logger.warning(f"⚠️ 数据库锁定，尝试 {attempt + 1}/{max_retries} 失败: {e}")
                    await db.rollback()

                    if attempt < max_retries - 1:
                        logger.info(f"🔄 等待 {retry_delay} 秒后重试...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        logger.error(f"❌ 重试 {max_retries} 次后仍然失败")
                        return {
                            "success": False,
                            "error": f"数据库锁定，重试失败: {e}",
                            "new_count": 0,
                            "updated_count": 0
                        }
                else:
                    logger.error(f"❌ 保存聊天数据失败: {e}")
                    await db.rollback()
                    return {
                        "success": False,
                        "error": str(e),
                        "new_count": 0,
                        "updated_count": 0
                    }
    
    @staticmethod
    async def _get_user_by_session(session_id: str, db: AsyncSession) -> Optional[User]:
        """根据session_id查找用户"""
        try:
            # 这里需要实现根据session_id查找用户的逻辑
            # 暂时使用一个简单的方法：从session_id中提取手机号
            phone_number = TelegramChatService._extract_phone_from_session(session_id)
            if phone_number:
                result = await db.execute(
                    select(User).where(User.phone_number == phone_number)
                )
                return result.scalar_one_or_none()
            return None
        except Exception as e:
            logger.error(f"❌ 根据session查找用户失败: {e}")
            return None
    
    @staticmethod
    async def _get_or_create_user_by_session(session_id: str, db: AsyncSession) -> Optional[User]:
        """根据session_id查找或创建用户"""
        try:
            # 先尝试查找现有用户
            user = await TelegramChatService._get_user_by_session(session_id, db)
            if user:
                return user
            
            # 如果用户不存在，创建新用户
            phone_number = TelegramChatService._extract_phone_from_session(session_id)
            if phone_number:
                user = await UserCRUD.create_user(
                    db,
                    telegram_user_id=0,  # 暂时使用0，后续可以从session中获取真实ID
                    username=f"user_{phone_number}",
                    first_name="Unknown",
                    phone_number=phone_number
                )
                logger.info(f"✅ 创建新用户: {phone_number}")
                return user
            
            return None
        except Exception as e:
            logger.error(f"❌ 获取或创建用户失败: {e}")
            return None
    
    @staticmethod
    def _extract_phone_from_session(session_id: str) -> Optional[str]:
        """从session_id中提取手机号"""
        try:
            # session_id通常包含手机号信息
            # 这里需要根据实际的session_id格式来解析
            # 暂时返回一个默认值，后续需要完善
            return "8613965150331"  # 临时硬编码，需要改进
        except Exception as e:
            logger.error(f"❌ 从session提取手机号失败: {e}")
            return None
