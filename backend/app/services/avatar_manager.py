"""
头像管理器 - 参考Nicegram/Swiftgram的头像处理逻辑
支持用户、群组、频道头像的智能缓存和管理
"""

import os
import time
import hashlib
import asyncio
from pathlib import Path
from typing import Optional, Dict, List, Tuple
from telethon import TelegramClient
from telethon.tl.types import User, Chat, Channel, UserProfilePhoto, ChatPhoto
import json
from datetime import datetime, timedelta

class AvatarManager:
    """
    头像管理器 - 参考第三方客户端的最佳实践
    """
    
    def __init__(self, storage_path: str = "storage/avatars"):
        self.storage_path = Path(storage_path)
        self.cache_db_path = self.storage_path / "cache.json"
        
        # 创建目录结构
        self.storage_path.mkdir(parents=True, exist_ok=True)
        (self.storage_path / "users").mkdir(exist_ok=True)
        (self.storage_path / "chats").mkdir(exist_ok=True)
        (self.storage_path / "channels").mkdir(exist_ok=True)
        (self.storage_path / "thumbnails").mkdir(exist_ok=True)
        
        # 内存缓存
        self.memory_cache = {}
        
        # 加载缓存数据库
        self.cache_db = self._load_cache_db()
        
        print(f"📁 头像管理器初始化完成: {self.storage_path.absolute()}")
    
    def _load_cache_db(self) -> Dict:
        """加载缓存数据库"""
        try:
            if self.cache_db_path.exists():
                with open(self.cache_db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"❌ 加载缓存数据库失败: {e}")
        
        return {
            "users": {},
            "chats": {},
            "channels": {},
            "last_cleanup": time.time()
        }
    
    def _save_cache_db(self):
        """保存缓存数据库"""
        try:
            with open(self.cache_db_path, 'w', encoding='utf-8') as f:
                json.dump(self.cache_db, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存缓存数据库失败: {e}")
    
    def _get_avatar_key(self, entity_id: int, entity_type: str, photo_id: Optional[str] = None) -> str:
        """生成头像缓存键"""
        if photo_id:
            return f"{entity_type}_{entity_id}_{photo_id}"
        return f"{entity_type}_{entity_id}"
    
    def _get_avatar_filename(self, entity_id: int, entity_type: str, photo_id: Optional[str] = None, is_thumbnail: bool = False) -> str:
        """生成头像文件名"""
        suffix = "_thumb" if is_thumbnail else ""
        if photo_id:
            return f"{entity_id}_{photo_id}{suffix}.jpg"
        return f"{entity_id}_{int(time.time())}{suffix}.jpg"
    
    def _get_avatar_path(self, entity_type: str, filename: str, is_thumbnail: bool = False) -> Path:
        """获取头像文件路径"""
        if is_thumbnail:
            return self.storage_path / "thumbnails" / filename
        return self.storage_path / f"{entity_type}s" / filename
    
    async def get_user_avatar(self, client: TelegramClient, user: User, force_refresh: bool = False) -> Optional[str]:
        """
        获取用户头像 - 参考Nicegram的处理逻辑
        
        Args:
            client: Telegram客户端
            user: 用户对象
            force_refresh: 是否强制刷新
            
        Returns:
            头像URL或None
        """
        try:
            entity_type = "user"
            entity_id = user.id
            
            # 检查用户是否有头像
            if not user.photo:
                print(f"👤 用户 {user.first_name} (ID: {entity_id}) 没有头像")
                return None
            
            # 获取头像ID（用于检测头像是否更新）
            photo_id = str(user.photo.photo_id) if hasattr(user.photo, 'photo_id') else None
            cache_key = self._get_avatar_key(entity_id, entity_type, photo_id)
            
            # 检查内存缓存
            if not force_refresh and cache_key in self.memory_cache:
                print(f"💾 从内存缓存获取用户头像: {entity_id}")
                return self.memory_cache[cache_key]
            
            # 检查磁盘缓存
            if not force_refresh and entity_type in self.cache_db and str(entity_id) in self.cache_db[entity_type]:
                cached_info = self.cache_db[entity_type][str(entity_id)]

                # 检查头像是否已更新（如果没有photo_id，则检查文件是否存在且不超过24小时）
                cache_valid = False
                if photo_id and cached_info.get('photo_id') == photo_id:
                    cache_valid = True
                elif not photo_id:
                    # 如果没有photo_id，检查缓存时间（24小时内有效）
                    cache_time = cached_info.get('downloaded_at', 0)
                    if time.time() - cache_time < 24 * 60 * 60:
                        cache_valid = True

                if cache_valid:
                    avatar_path = self._get_avatar_path(entity_type, cached_info['filename'])
                    if avatar_path.exists():
                        avatar_url = f"/api/v1/avatars/{entity_type}s/{cached_info['filename']}"
                        self.memory_cache[cache_key] = avatar_url
                        print(f"💿 从磁盘缓存获取用户头像: {entity_id}")
                        return avatar_url
            
            # 下载新头像
            print(f"📸 下载用户头像: {user.first_name} (ID: {entity_id})")
            
            # 生成文件名
            filename = self._get_avatar_filename(entity_id, entity_type, photo_id)
            avatar_path = self._get_avatar_path(entity_type, filename)
            
            # 下载头像
            await client.download_profile_photo(user, file=str(avatar_path))
            
            # 生成缩略图（可选）
            thumb_filename = self._get_avatar_filename(entity_id, entity_type, photo_id, is_thumbnail=True)
            # TODO: 生成缩略图逻辑
            
            # 更新缓存数据库
            if entity_type not in self.cache_db:
                self.cache_db[entity_type] = {}
            
            self.cache_db[entity_type][str(entity_id)] = {
                "filename": filename,
                "photo_id": photo_id,
                "downloaded_at": time.time(),
                "entity_name": user.first_name,
                "file_size": avatar_path.stat().st_size if avatar_path.exists() else 0
            }
            
            self._save_cache_db()
            
            # 生成URL并缓存到内存
            avatar_url = f"/api/v1/avatars/{entity_type}s/{filename}"
            self.memory_cache[cache_key] = avatar_url
            
            print(f"✅ 用户头像下载成功: {avatar_url}")
            return avatar_url
            
        except Exception as e:
            print(f"❌ 获取用户头像失败: {e}")
            return None
    
    async def get_chat_avatar(self, client: TelegramClient, chat: Chat, force_refresh: bool = False) -> Optional[str]:
        """获取群组头像"""
        try:
            entity_type = "chat"
            entity_id = chat.id
            
            if not chat.photo:
                print(f"👥 群组 {chat.title} (ID: {entity_id}) 没有头像")
                return None
            
            photo_id = str(chat.photo.photo_id) if hasattr(chat.photo, 'photo_id') else None
            cache_key = self._get_avatar_key(entity_id, entity_type, photo_id)
            
            # 类似用户头像的处理逻辑...
            # 这里简化实现，实际应该和用户头像逻辑一致
            
            print(f"📸 下载群组头像: {chat.title} (ID: {entity_id})")
            
            filename = self._get_avatar_filename(entity_id, entity_type, photo_id)
            avatar_path = self._get_avatar_path(entity_type, filename)
            
            await client.download_profile_photo(chat, file=str(avatar_path))
            
            # 更新缓存
            if entity_type not in self.cache_db:
                self.cache_db[entity_type] = {}
            
            self.cache_db[entity_type][str(entity_id)] = {
                "filename": filename,
                "photo_id": photo_id,
                "downloaded_at": time.time(),
                "entity_name": chat.title,
                "file_size": avatar_path.stat().st_size if avatar_path.exists() else 0
            }
            
            self._save_cache_db()
            
            avatar_url = f"/api/v1/avatars/{entity_type}s/{filename}"
            self.memory_cache[cache_key] = avatar_url
            
            print(f"✅ 群组头像下载成功: {avatar_url}")
            return avatar_url
            
        except Exception as e:
            print(f"❌ 获取群组头像失败: {e}")
            return None
    
    async def batch_download_avatars(self, client: TelegramClient, entities: List[Tuple[int, str]], max_concurrent: int = 5):
        """
        批量下载头像 - 参考第三方客户端的批量处理
        
        Args:
            client: Telegram客户端
            entities: [(entity_id, entity_type), ...] 列表
            max_concurrent: 最大并发数
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def download_single(entity_id: int, entity_type: str):
            async with semaphore:
                try:
                    if entity_type == "user":
                        user = await client.get_entity(entity_id)
                        await self.get_user_avatar(client, user)
                    elif entity_type == "chat":
                        chat = await client.get_entity(entity_id)
                        await self.get_chat_avatar(client, chat)
                    # 添加延迟避免API限制
                    await asyncio.sleep(0.1)
                except Exception as e:
                    print(f"❌ 批量下载头像失败 {entity_type}:{entity_id} - {e}")
        
        tasks = [download_single(entity_id, entity_type) for entity_id, entity_type in entities]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        print(f"✅ 批量下载完成，处理了 {len(entities)} 个头像")
    
    def cleanup_old_avatars(self, days: int = 30):
        """清理旧头像文件"""
        try:
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            cleaned_count = 0
            
            for entity_type in ["users", "chats", "channels"]:
                entity_path = self.storage_path / entity_type
                if entity_path.exists():
                    for avatar_file in entity_path.glob("*.jpg"):
                        if avatar_file.stat().st_mtime < cutoff_time:
                            avatar_file.unlink()
                            cleaned_count += 1
            
            # 清理缓存数据库中的过期记录
            for entity_type in ["users", "chats", "channels"]:
                if entity_type in self.cache_db:
                    expired_keys = []
                    for entity_id, info in self.cache_db[entity_type].items():
                        if info.get('downloaded_at', 0) < cutoff_time:
                            expired_keys.append(entity_id)
                    
                    for key in expired_keys:
                        del self.cache_db[entity_type][key]
            
            self.cache_db['last_cleanup'] = time.time()
            self._save_cache_db()
            
            print(f"✅ 清理完成，删除了 {cleaned_count} 个旧头像文件")
            
        except Exception as e:
            print(f"❌ 清理头像文件失败: {e}")
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        stats = {
            "total_users": len(self.cache_db.get("users", {})),
            "total_chats": len(self.cache_db.get("chats", {})),
            "total_channels": len(self.cache_db.get("channels", {})),
            "memory_cache_size": len(self.memory_cache),
            "last_cleanup": self.cache_db.get("last_cleanup", 0)
        }
        
        # 计算磁盘使用量
        total_size = 0
        for entity_type in ["users", "chats", "channels"]:
            entity_path = self.storage_path / entity_type
            if entity_path.exists():
                for avatar_file in entity_path.glob("*.jpg"):
                    total_size += avatar_file.stat().st_size
        
        stats["disk_usage_mb"] = round(total_size / (1024 * 1024), 2)
        
        return stats

# 全局头像管理器实例
avatar_manager = AvatarManager()
