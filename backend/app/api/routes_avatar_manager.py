"""
头像管理API - 参考第三方客户端的头像处理逻辑
"""

from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from app.services.avatar_manager import avatar_manager

router = APIRouter()

class BatchDownloadRequest(BaseModel):
    """批量下载请求"""
    entities: List[Dict[str, Any]]  # [{"id": 123, "type": "user"}, ...]
    max_concurrent: Optional[int] = 5

class AvatarCacheInfo(BaseModel):
    """头像缓存信息"""
    entity_id: int
    entity_type: str
    has_avatar: bool
    cached: bool
    avatar_url: Optional[str] = None
    cache_time: Optional[float] = None

@router.get("/avatar-manager/stats")
async def get_avatar_cache_stats():
    """
    获取头像缓存统计信息
    类似于Nicegram的缓存管理界面
    """
    try:
        stats = avatar_manager.get_cache_stats()
        
        return {
            "data": {
                "cache_stats": stats,
                "storage_path": str(avatar_manager.storage_path),
                "recommendations": _get_cache_recommendations(stats)
            },
            "success": True,
            "message": "获取头像缓存统计成功"
        }
        
    except Exception as e:
        print(f"❌ 获取头像缓存统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取头像缓存统计失败: {str(e)}"
        )

def _get_cache_recommendations(stats: Dict) -> List[str]:
    """生成缓存优化建议"""
    recommendations = []
    
    if stats["disk_usage_mb"] > 100:
        recommendations.append("磁盘使用量较大，建议清理旧头像")
    
    if stats["memory_cache_size"] > 1000:
        recommendations.append("内存缓存较大，考虑重启应用释放内存")
    
    if stats["last_cleanup"] < (time.time() - 7 * 24 * 60 * 60):
        recommendations.append("超过7天未清理，建议执行清理操作")
    
    if not recommendations:
        recommendations.append("缓存状态良好，无需特殊操作")
    
    return recommendations

@router.post("/avatar-manager/batch-download")
async def batch_download_avatars(request: BatchDownloadRequest, background_tasks: BackgroundTasks):
    """
    批量下载头像
    类似于Nicegram的批量预加载功能
    """
    try:
        # 验证请求
        if not request.entities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="实体列表不能为空"
            )
        
        if len(request.entities) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="单次批量下载不能超过100个实体"
            )
        
        # 转换为内部格式
        entities = []
        for entity in request.entities:
            if "id" not in entity or "type" not in entity:
                continue
            if entity["type"] not in ["user", "chat", "channel"]:
                continue
            entities.append((entity["id"], entity["type"]))
        
        if not entities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有有效的实体"
            )
        
        # 在后台执行批量下载
        # 注意：这里需要一个有效的Telegram客户端
        # 实际实现中需要从认证的客户端池中获取
        print(f"🚀 开始批量下载 {len(entities)} 个头像...")
        
        # background_tasks.add_task(
        #     avatar_manager.batch_download_avatars,
        #     client,  # 需要传入有效的客户端
        #     entities,
        #     request.max_concurrent
        # )
        
        return {
            "data": {
                "queued_count": len(entities),
                "max_concurrent": request.max_concurrent,
                "estimated_time_seconds": len(entities) * 0.5  # 估算时间
            },
            "success": True,
            "message": f"已将 {len(entities)} 个头像加入下载队列"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 批量下载头像失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量下载头像失败: {str(e)}"
        )

@router.get("/avatar-manager/cache/{entity_type}/{entity_id}")
async def get_avatar_cache_info(entity_type: str, entity_id: int):
    """
    获取特定实体的头像缓存信息
    """
    try:
        if entity_type not in ["user", "chat", "channel"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的实体类型"
            )
        
        # 检查缓存数据库
        cached = False
        avatar_url = None
        cache_time = None
        
        if entity_type in avatar_manager.cache_db and str(entity_id) in avatar_manager.cache_db[entity_type]:
            cached_info = avatar_manager.cache_db[entity_type][str(entity_id)]
            cached = True
            avatar_url = f"/api/v1/avatars/{entity_type}s/{cached_info['filename']}"
            cache_time = cached_info.get('downloaded_at')
        
        return {
            "data": {
                "entity_id": entity_id,
                "entity_type": entity_type,
                "cached": cached,
                "avatar_url": avatar_url,
                "cache_time": cache_time,
                "cache_age_hours": (time.time() - cache_time) / 3600 if cache_time else None
            },
            "success": True,
            "message": "获取头像缓存信息成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 获取头像缓存信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取头像缓存信息失败: {str(e)}"
        )

@router.delete("/avatar-manager/cleanup")
async def cleanup_avatar_cache(days: int = 30):
    """
    清理头像缓存
    类似于Nicegram的缓存清理功能
    """
    try:
        if days < 1 or days > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="清理天数必须在1-365之间"
            )
        
        # 获取清理前的统计
        before_stats = avatar_manager.get_cache_stats()
        
        # 执行清理
        avatar_manager.cleanup_old_avatars(days)
        
        # 获取清理后的统计
        after_stats = avatar_manager.get_cache_stats()
        
        # 计算清理效果
        freed_space_mb = before_stats["disk_usage_mb"] - after_stats["disk_usage_mb"]
        freed_users = before_stats["total_users"] - after_stats["total_users"]
        freed_chats = before_stats["total_chats"] - after_stats["total_chats"]
        freed_channels = before_stats["total_channels"] - after_stats["total_channels"]
        
        return {
            "data": {
                "cleanup_days": days,
                "before_stats": before_stats,
                "after_stats": after_stats,
                "freed_space_mb": round(freed_space_mb, 2),
                "freed_entities": {
                    "users": freed_users,
                    "chats": freed_chats,
                    "channels": freed_channels,
                    "total": freed_users + freed_chats + freed_channels
                }
            },
            "success": True,
            "message": f"头像缓存清理完成，释放了 {freed_space_mb:.2f}MB 空间"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 清理头像缓存失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理头像缓存失败: {str(e)}"
        )

@router.get("/avatar-manager/recommendations")
async def get_cache_recommendations():
    """
    获取缓存优化建议
    类似于第三方客户端的智能建议功能
    """
    try:
        stats = avatar_manager.get_cache_stats()
        recommendations = []
        
        # 存储空间建议
        if stats["disk_usage_mb"] > 500:
            recommendations.append({
                "type": "storage",
                "priority": "high",
                "title": "存储空间不足",
                "description": f"头像缓存占用 {stats['disk_usage_mb']:.1f}MB，建议清理旧文件",
                "action": "cleanup",
                "action_params": {"days": 15}
            })
        elif stats["disk_usage_mb"] > 200:
            recommendations.append({
                "type": "storage",
                "priority": "medium",
                "title": "存储空间较大",
                "description": f"头像缓存占用 {stats['disk_usage_mb']:.1f}MB，可考虑清理",
                "action": "cleanup",
                "action_params": {"days": 30}
            })
        
        # 缓存效率建议
        total_entities = stats["total_users"] + stats["total_chats"] + stats["total_channels"]
        if total_entities > 1000:
            recommendations.append({
                "type": "performance",
                "priority": "medium",
                "title": "缓存实体较多",
                "description": f"已缓存 {total_entities} 个实体的头像，建议定期清理",
                "action": "cleanup",
                "action_params": {"days": 20}
            })
        
        # 内存使用建议
        if stats["memory_cache_size"] > 500:
            recommendations.append({
                "type": "memory",
                "priority": "low",
                "title": "内存缓存较大",
                "description": f"内存中缓存了 {stats['memory_cache_size']} 个头像URL",
                "action": "restart",
                "action_params": {}
            })
        
        if not recommendations:
            recommendations.append({
                "type": "info",
                "priority": "low",
                "title": "缓存状态良好",
                "description": "头像缓存运行正常，无需特殊操作",
                "action": "none",
                "action_params": {}
            })
        
        return {
            "data": {
                "recommendations": recommendations,
                "stats": stats,
                "last_check": time.time()
            },
            "success": True,
            "message": "获取缓存建议成功"
        }
        
    except Exception as e:
        print(f"❌ 获取缓存建议失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取缓存建议失败: {str(e)}"
        )

import time
