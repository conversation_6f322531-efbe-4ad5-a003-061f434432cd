"""
真实Telegram API集成
使用Telethon库实现真实的Telegram验证码登录
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Optional
from pydantic import BaseModel
import hashlib
import time
import asyncio
import os
from telethon import TelegramClient
from telethon.errors import (
    PhoneNumberInvalidError, 
    PhoneCodeInvalidError, 
    PhoneCodeExpiredError,
    SessionPasswordNeededError,
    PasswordHashInvalidError,
    ApiIdInvalidError,
    FloodWaitError
)

# 请求/响应模型
class AuthRequest(BaseModel):
    phone_number: Optional[str] = None
    api_id: Optional[int] = None
    api_hash: Optional[str] = None
    phone_code_hash: Optional[str] = None
    code: Optional[str] = None
    password: Optional[str] = None

class AuthResponse(BaseModel):
    access_token: str
    user_id: int
    token_type: str = "bearer"

# 创建路由器
router = APIRouter()

# 全局变量存储Telegram客户端会话和用户信息
telegram_clients = {}
current_user_data = {}
# 存储已认证的Telegram客户端（用于获取用户信息）
authenticated_clients = {}

# ==================== CORS预检请求处理 ====================

@router.options("/auth/{path:path}")
async def auth_options():
    """处理认证相关的CORS预检请求"""
    return {"message": "OK"}

# ==================== 真实Telegram API接口 ====================

@router.post("/auth/request_code")
async def request_code(request: AuthRequest):
    """请求发送真实Telegram验证码"""
    try:
        print("=" * 80)
        print("🚨 DEBUG: 真实Telegram验证码发送请求开始")
        print("=" * 80)
        print(f"📥 接收到的请求数据:")
        print(f"   - 手机号: {request.phone_number}")
        print(f"   - API ID: {request.api_id}")
        print(f"   - API Hash: {request.api_hash}")
        print(f"   - 请求时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 验证必需参数
        if not request.phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号不能为空"
            )
        
        if not request.api_id or not request.api_hash:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="API ID 和 API Hash 不能为空"
            )

        print(f"\n🔧 创建Telegram客户端:")
        print(f"   - API ID: {request.api_id}")
        print(f"   - 手机号: {request.phone_number}")

        # 创建Telegram客户端
        session_name = f"session_{request.phone_number}_{request.api_id}"
        client = TelegramClient(session_name, request.api_id, request.api_hash)
        
        print(f"   - 会话名称: {session_name}")
        print(f"   - 正在连接到Telegram服务器...")

        # 连接到Telegram
        await client.connect()
        
        if not await client.is_user_authorized():
            print(f"   - 用户未授权，发送验证码...")
            
            # 发送验证码
            sent_code = await client.send_code_request(request.phone_number)
            phone_code_hash = sent_code.phone_code_hash
            
            # 存储客户端会话
            telegram_clients[phone_code_hash] = client
            
            print(f"\n✅ 验证码发送成功:")
            print(f"   - phone_code_hash: {phone_code_hash}")
            print(f"   - 验证码已发送到您的Telegram客户端")
            print(f"   - 请检查您的Telegram应用")
            
            print("=" * 80)
            print("🚨 DEBUG: 真实Telegram验证码发送完成")
            print("=" * 80)

            return {
                "data": {"phone_code_hash": phone_code_hash},
                "success": True,
                "message": "验证码已发送到您的Telegram客户端，请检查Telegram应用"
            }
        else:
            print(f"   - 用户已授权，无需验证码")
            await client.disconnect()
            return {
                "data": {"phone_code_hash": "already_authorized"},
                "success": True,
                "message": "用户已授权，无需验证码"
            }

    except ApiIdInvalidError:
        print(f"❌ API ID 无效")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="API ID 无效，请检查您的Telegram API凭据"
        )
    except PhoneNumberInvalidError:
        print(f"❌ 手机号无效")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号格式无效，请使用国际格式（如+8613800138000）"
        )
    except FloodWaitError as e:
        print(f"❌ 请求过于频繁，需要等待 {e.seconds} 秒")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"请求过于频繁，请等待 {e.seconds} 秒后重试"
        )
    except Exception as e:
        print(f"❌ 验证码发送失败: {e}")
        print(f"❌ 错误详情: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送验证码失败: {str(e)}"
        )

@router.post("/auth/login_phone")
async def login_phone(request: AuthRequest):
    """使用真实Telegram验证码登录"""
    try:
        print("=" * 80)
        print("🚨 DEBUG: 真实Telegram验证码登录开始")
        print("=" * 80)
        print(f"📥 接收到的登录数据:")
        print(f"   - 手机号: {request.phone_number}")
        print(f"   - 验证码: {request.code}")
        print(f"   - phone_code_hash: {request.phone_code_hash}")
        print(f"   - 请求时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 验证必需参数
        if not request.phone_number or not request.code or not request.phone_code_hash:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号、验证码和phone_code_hash都不能为空"
            )

        # 获取存储的客户端
        client = telegram_clients.get(request.phone_code_hash)
        if not client:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的phone_code_hash或会话已过期，请重新请求验证码"
            )

        print(f"\n🔍 验证码登录:")
        print(f"   - 使用验证码: {request.code}")
        print(f"   - 正在验证...")

        try:
            # 使用验证码登录
            user = await client.sign_in(
                phone=request.phone_number,
                code=request.code,
                phone_code_hash=request.phone_code_hash
            )
            
            print(f"   ✅ 验证码验证成功")
            print(f"   - 用户ID: {user.id}")
            print(f"   - 用户名: {user.first_name} {user.last_name or ''}")
            print(f"   - 用户名: @{user.username or 'N/A'}")

            # 生成访问令牌
            access_token = hashlib.md5(f"telegram_{user.id}_{time.time()}".encode()).hexdigest()

            # 存储真实用户数据
            user_data = {
                "user_id": user.id,
                "telegram_user_id": user.id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "username": user.username,
                "phone_number": user.phone,
                "created_at": time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                "updated_at": time.strftime('%Y-%m-%dT%H:%M:%SZ')
            }
            current_user_data[access_token] = user_data

            # 保存已认证的客户端用于后续获取用户信息
            authenticated_clients[user.id] = {
                "client": client,
                "user_data": user_data,
                "access_token": access_token
            }

            print(f"   💾 存储用户数据: {user_data}")
            print(f"   🔐 保存已认证客户端: {user.id}")

            # 不要断开客户端连接，保持用于后续使用
            # await client.disconnect()
            del telegram_clients[request.phone_code_hash]

            print("=" * 80)
            print("🚨 DEBUG: 真实Telegram验证码登录完成 - 登录成功")
            print("=" * 80)

            return {
                "data": {
                    "access_token": access_token,
                    "user_id": user.id,
                    "token_type": "bearer",
                    "telegram_user": {
                        "id": user.id,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "username": user.username,
                        "phone": user.phone
                    }
                },
                "success": True,
                "message": "Telegram登录成功！"
            }

        except SessionPasswordNeededError:
            print(f"   ⚠️ 需要两步验证密码")
            # 不断开连接，保持会话用于密码验证
            return {
                "data": {
                    "requires_password": True,
                    "phone_code_hash": request.phone_code_hash
                },
                "success": True,
                "message": "需要输入两步验证密码"
            }

    except PhoneCodeInvalidError:
        print(f"❌ 验证码无效")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="验证码无效，请检查输入的验证码"
        )
    except PhoneCodeExpiredError:
        print(f"❌ 验证码已过期")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="验证码已过期，请重新请求验证码"
        )
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        print(f"❌ 错误详情: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@router.post("/auth/login_password")
async def login_password(request: AuthRequest):
    """使用两步验证密码登录"""
    try:
        print("=" * 80)
        print("🚨 DEBUG: 两步验证密码登录开始")
        print("=" * 80)
        
        if not request.password or not request.phone_code_hash:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码和phone_code_hash都不能为空"
            )

        # 获取存储的客户端
        client = telegram_clients.get(request.phone_code_hash)
        if not client:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的phone_code_hash或会话已过期"
            )

        print(f"   - 正在验证两步验证密码...")

        # 使用密码完成登录
        user = await client.sign_in(password=request.password)
        
        print(f"   ✅ 密码验证成功")
        print(f"   - 用户ID: {user.id}")

        # 生成访问令牌
        access_token = hashlib.md5(f"telegram_{user.id}_{time.time()}".encode()).hexdigest()

        # 存储真实用户数据
        user_data = {
            "user_id": user.id,
            "telegram_user_id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "username": user.username,
            "phone_number": user.phone,
            "created_at": time.strftime('%Y-%m-%dT%H:%M:%SZ'),
            "updated_at": time.strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        current_user_data[access_token] = user_data

        # 保存已认证的客户端用于后续获取用户信息
        authenticated_clients[user.id] = {
            "client": client,
            "user_data": user_data,
            "access_token": access_token
        }

        print(f"   💾 存储用户数据: {user_data}")
        print(f"   🔐 保存已认证客户端: {user.id}")

        # 不要断开客户端连接，保持用于后续使用
        # await client.disconnect()
        del telegram_clients[request.phone_code_hash]

        print("=" * 80)
        print("🚨 DEBUG: 两步验证密码登录完成 - 登录成功")
        print("=" * 80)

        return {
            "data": {
                "access_token": access_token,
                "user_id": user.id,
                "token_type": "bearer",
                "telegram_user": {
                    "id": user.id,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "username": user.username,
                    "phone": user.phone
                }
            },
            "success": True,
            "message": "两步验证成功，登录完成！"
        }

    except PasswordHashInvalidError:
        print(f"❌ 密码无效")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="两步验证密码无效"
        )
    except Exception as e:
        print(f"❌ 密码验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"密码验证失败: {str(e)}"
        )

# ==================== 其他接口 ====================

@router.get("/auth/me")
async def get_current_user_info():
    """获取当前用户信息"""
    try:
        print("=" * 80)
        print("🚨 DEBUG: 获取当前用户信息")
        print("=" * 80)

        # 方法1: 从已认证的客户端获取最新用户信息
        if authenticated_clients:
            # 获取最新登录的用户（最后一个）
            latest_user_id = list(authenticated_clients.keys())[-1]
            client_info = authenticated_clients[latest_user_id]
            client = client_info["client"]

            print(f"🔍 找到已认证客户端，用户ID: {latest_user_id}")

            try:
                # 从Telegram获取最新的用户信息
                me = await client.get_me()

                # 构建最新的用户数据
                user_data = {
                    "user_id": me.id,
                    "telegram_user_id": me.id,
                    "first_name": me.first_name,
                    "last_name": me.last_name,
                    "username": me.username,
                    "phone_number": me.phone,
                    "created_at": time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "updated_at": time.strftime('%Y-%m-%dT%H:%M:%SZ')
                }

                print(f"📊 从Telegram获取到最新用户数据:")
                print(f"   - 用户ID: {user_data['user_id']}")
                print(f"   - 姓名: {user_data['first_name']} {user_data['last_name'] or ''}")
                print(f"   - 用户名: @{user_data['username'] or 'N/A'}")
                print(f"   - 手机号: {user_data['phone_number']}")

                print("=" * 80)
                print("🚨 DEBUG: 返回真实用户数据完成")
                print("=" * 80)

                return {
                    "data": user_data,
                    "success": True,
                    "message": "获取用户信息成功"
                }

            except Exception as client_error:
                print(f"❌ 从Telegram客户端获取用户信息失败: {client_error}")
                # 如果客户端失败，使用存储的用户数据
                user_data = client_info["user_data"]

                print(f"📊 使用存储的用户数据:")
                print(f"   - 用户ID: {user_data['user_id']}")
                print(f"   - 姓名: {user_data['first_name']} {user_data['last_name'] or ''}")

                return {
                    "data": user_data,
                    "success": True,
                    "message": "获取用户信息成功（存储数据）"
                }

        # 方法2: 从内存存储获取用户数据
        elif current_user_data:
            # 获取最新的用户数据（最后一个登录的用户）
            latest_token = list(current_user_data.keys())[-1]
            user_data = current_user_data[latest_token]

            print(f"📊 从内存获取用户数据:")
            print(f"   - 用户ID: {user_data['user_id']}")
            print(f"   - 姓名: {user_data['first_name']} {user_data['last_name'] or ''}")

            return {
                "data": user_data,
                "success": True,
                "message": "获取用户信息成功（内存数据）"
            }

        # 方法3: 从现有session文件获取真实用户数据
        else:
            print("❌ 未找到内存中的用户数据，尝试从session文件获取")
            print("=" * 80)

            try:
                import glob
                import os

                # 查找所有session文件
                session_files = glob.glob("session_*.session")
                print(f"🔍 找到 {len(session_files)} 个session文件: {session_files}")

                if session_files:
                    # 尝试所有session文件，直到找到一个有效的
                    for session_file in session_files:
                        print(f"📱 尝试session文件: {session_file}")

                        # 从文件名提取手机号
                        import re
                        phone_match = re.search(r'session_(\+\d+)_', session_file)
                        phone_number = phone_match.group(1) if phone_match else None
                        print(f"   📞 提取的手机号: {phone_number}")

                        # 尝试连接并获取用户信息
                        try:
                            # 创建临时客户端获取用户信息
                            temp_client = TelegramClient(session_file.replace('.session', ''),
                                                       api_id=17462854,
                                                       api_hash="344583e45741c457fe1862106095a5eb")

                            await temp_client.connect()

                            if await temp_client.is_user_authorized():
                                print(f"✅ Session {session_file} 已授权，获取用户信息...")

                                # 获取真实用户信息
                                me = await temp_client.get_me()

                                real_user_data = {
                                    "user_id": me.id,
                                    "telegram_user_id": me.id,
                                    "first_name": me.first_name,
                                    "last_name": me.last_name,
                                    "username": me.username,
                                    "phone_number": me.phone,
                                    "avatar_url": None,
                                    "created_at": time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                                    "updated_at": time.strftime('%Y-%m-%dT%H:%M:%SZ')
                                }

                                # 尝试获取用户头像URL
                                try:
                                    from app.services.avatar_service import avatar_service
                                    avatar_url = avatar_service.get_avatar_url(
                                        avatar_path=None,
                                        entity_type="user",
                                        entity_id=real_user_data['user_id'],
                                        entity_name=real_user_data['first_name']
                                    )
                                    real_user_data['avatar_url'] = avatar_url
                                except Exception as e:
                                    print(f"   - 头像获取失败: {e}")
                                    real_user_data['avatar_url'] = f"https://ui-avatars.com/api/?name={real_user_data['first_name']}&size=128&background=3b82f6&color=ffffff&bold=true"

                                await temp_client.disconnect()

                                print(f"✅ 从session获取到真实用户数据:")
                                print(f"   - 用户ID: {real_user_data['user_id']}")
                                print(f"   - 姓名: {real_user_data['first_name']} {real_user_data['last_name'] or ''}")
                                print(f"   - 用户名: @{real_user_data['username'] or 'N/A'}")
                                print(f"   - 手机号: {real_user_data['phone_number']}")
                                print(f"   - 头像: {real_user_data['avatar_url']}")

                                return {
                                    "data": real_user_data,
                                    "success": True,
                                    "message": "获取用户信息成功（从session）"
                                }
                            else:
                                print(f"❌ Session {session_file} 未授权")
                                await temp_client.disconnect()
                        except Exception as session_error:
                            print(f"❌ Session {session_file} 连接失败: {session_error}")
                            try:
                                await temp_client.disconnect()
                            except:
                                pass

                # 如果所有方法都失败，返回错误
                print("❌ 无法获取任何用户数据")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户未登录或session已过期，请重新登录"
                )

            except Exception as e:
                print(f"❌ 获取session用户数据失败: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="获取用户信息失败，请重新登录"
                )

    except Exception as e:
        print(f"❌ 获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户信息失败: {str(e)}"
        )

@router.post("/auth/logout")
async def logout():
    """注销当前用户会话"""
    return {"message": "Logged out successfully"}

@router.get("/test")
async def test():
    """测试端点"""
    return {"message": "Real Telegram API is working!", "timestamp": time.time()}

@router.get("/health")
async def health():
    """健康检查端点"""
    return {"status": "healthy", "service": "TeleSeeker Real Telegram API", "timestamp": time.time()}
