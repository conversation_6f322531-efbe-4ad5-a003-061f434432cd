"""
Telegram Updates API 路由 - 简化版本
提供聊天列表相关的API端点
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from datetime import datetime
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.core import get_logger
from app.core.database import get_db_session
from app.services.telegram_chat_service import TelegramChatService

logger = get_logger(__name__)

router = APIRouter()


# 演示数据
DEMO_CHATS = [
    {
        "id": -1001234567890,
        "title": "📱 科技讨论群",
        "type": "supergroup",
        "username": "tech_discussion",
        "member_count": 156,
        "description": "分享最新科技动态和工具",
        "is_verified": False,
        "unread_count": 3,
        "last_message_date": datetime.now().isoformat(),
        "photo_url": None
    },
    {
        "id": -1001234567891,
        "title": "工作群",
        "type": "group",
        "member_count": 23,
        "description": "日常工作交流",
        "unread_count": 1,
        "last_message_date": datetime.now().isoformat(),
        "photo_url": None
    },
    {
        "id": 123456789,
        "title": "王五",
        "type": "private",
        "username": "wangwu",
        "unread_count": 1,
        "last_message_date": datetime.now().isoformat(),
        "photo_url": None
    },
    {
        "id": -1001234567892,
        "title": "🎮 游戏交流频道",
        "type": "channel",
        "username": "gaming_channel",
        "member_count": 2341,
        "description": "游戏资讯和攻略分享",
        "is_verified": True,
        "unread_count": 0,
        "last_message_date": datetime.now().isoformat(),
        "photo_url": None
    }
]


def _get_chat_type(entity) -> str:
    """确定聊天类型"""
    from telethon.tl.types import User, Chat, Channel

    if isinstance(entity, User):
        return "private"
    elif isinstance(entity, Chat):
        return "group"
    elif isinstance(entity, Channel):
        if entity.broadcast:
            return "channel"
        else:
            return "supergroup"
    else:
        return "unknown"


@router.get("/custom-groups")
async def get_custom_groups(
    session_id: Optional[str] = Query(None, description="Telegram session ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """
    获取用户自定义分组
    """
    try:
        if not session_id:
            logger.info("没有提供session_id，返回空的自定义分组")
            return {
                "success": True,
                "data": {
                    "groups": [],
                    "count": 0,
                    "is_demo": True
                }
            }

        # TODO: 从Telegram客户端获取自定义分组
        # 这里先返回演示数据
        demo_groups = [
            {
                "id": "custom_1",
                "title": "工作群组",
                "chat_count": 5,
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "custom_2",
                "title": "朋友圈",
                "chat_count": 8,
                "created_at": "2024-01-02T00:00:00Z"
            }
        ]

        return {
            "success": True,
            "data": {
                "groups": demo_groups,
                "count": len(demo_groups),
                "is_demo": True
            }
        }

    except Exception as e:
        logger.error(f"获取自定义分组失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": {
                "groups": [],
                "count": 0
            }
        }


@router.get("/chats-fast")
async def get_chats_fast_no_avatars(session_id: str = None):
    """
    快速获取聊天列表 - 不下载头像，立即返回
    """
    try:
        if not session_id:
            # 没有session_id，返回演示数据
            logger.info("没有提供session_id，返回演示数据")
            return await get_demo_chats()

        logger.info(f"🚀 快速获取聊天列表（不下载头像）: {session_id[:20]}...")

        from telethon import TelegramClient
        from telethon.sessions import StringSession

        # 使用session字符串创建客户端
        client = TelegramClient(StringSession(session_id), 0, "")
        await client.connect()

        # 检查是否已授权
        if not await client.is_user_authorized():
            await client.disconnect()
            logger.warning("⚠️ Session未授权，返回演示数据")
            return await get_demo_chats()

        # 获取聊天列表（不下载头像）
        chats = []
        limit = 50  # 减少数量，快速响应

        async for dialog in client.iter_dialogs(limit=limit):
            try:
                # 提取聊天信息（不下载头像，快速响应）
                chat_info = {
                    "id": dialog.id,
                    "title": dialog.title or dialog.name or "未知聊天",
                    "type": _get_chat_type(dialog.entity),
                    "username": getattr(dialog.entity, 'username', None),
                    "participants_count": getattr(dialog.entity, 'participants_count', None),
                    "description": getattr(dialog.entity, 'about', None),
                    "is_verified": getattr(dialog.entity, 'verified', False),
                    "is_scam": getattr(dialog.entity, 'scam', False),
                    "is_fake": getattr(dialog.entity, 'fake', False),
                    "unread_count": dialog.unread_count,
                    "is_pinned": dialog.pinned,
                    "last_message_date": dialog.date.isoformat() if dialog.date else None,
                    "avatar_url": None  # 快速响应，不下载头像
                }

                chats.append(chat_info)

            except Exception as e:
                logger.warning(f"⚠️ 处理对话失败: {e}")
                continue

        # 断开客户端连接
        await client.disconnect()

        logger.info(f"✅ 快速获取到 {len(chats)} 个聊天")

        return {
            "success": True,
            "data": {
                "chats": chats,
                "count": len(chats),
                "cached": False,
                "is_demo": False,
                "experience_level": "authenticated",
                "session_id": session_id,
                "sync_info": {
                    "total_chats": len(chats),
                    "timestamp": datetime.now().isoformat()
                }
            }
        }

    except Exception as e:
        logger.error(f"❌ 快速获取聊天列表失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": {
                "chats": [],
                "count": 0,
                "is_demo": True
            }
        }


@router.get("/chats")
async def get_chats_by_session(session_id: str = None, db: AsyncSession = Depends(get_db_session)):
    """
    通过session_id获取聊天列表
    1. 先返回本地缓存数据（快速响应）
    2. 然后同步远程数据到数据库
    """
    try:
        if not session_id:
            # 没有session_id，返回演示数据
            return {
                "success": True,
                "data": {
                    "chats": DEMO_CHATS,
                    "count": len(DEMO_CHATS),
                    "cached": False,
                    "is_demo": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

        logger.info(f"获取聊天列表，session_id: {session_id}")

        # 1. 先获取本地缓存数据
        cached_result = await TelegramChatService.get_cached_chats(session_id, db)

        if cached_result.get("success") and cached_result.get("count") > 0:
            # 有缓存数据，立即返回缓存，然后在后台同步
            logger.info(f"✅ 返回缓存数据: {cached_result.get('count')} 个聊天")

            # 启动后台同步任务（不等待完成）
            import asyncio
            asyncio.create_task(sync_chats_in_background(session_id, db))

            return {
                "success": True,
                "data": {
                    "chats": cached_result["chats"],
                    "count": cached_result["count"],
                    "cached": True,
                    "is_demo": False,
                    "experience_level": "authenticated",
                    "session_id": session_id,
                    "last_sync": cached_result.get("last_sync")
                }
            }
        else:
            # 没有缓存数据，获取真实的Telegram数据（但不下载头像）
            logger.info("❌ 没有缓存数据，从Telegram获取基础数据...")

            try:
                # 直接使用session字符串创建客户端获取聊天列表
                from telethon import TelegramClient
                from telethon.sessions import StringSession

                logger.info(f"🔄 使用session字符串创建客户端: {session_id[:20]}...")

                # 使用session字符串创建客户端
                client = TelegramClient(StringSession(session_id), 0, "")
                await client.connect()

                # 检查是否已授权
                if not await client.is_user_authorized():
                    await client.disconnect()
                    raise HTTPException(
                        status_code=401,
                        detail="Session已过期或无效，请重新登录"
                    )

                # 获取聊天列表
                chats = []
                chat_count = 0
                limit = 100

                logger.info(f"📋 开始获取对话列表，限制: {limit}")

                async for dialog in client.iter_dialogs(limit=limit):
                    try:
                        # 提取聊天信息（不下载头像，快速响应）
                        chat_info = {
                            "id": dialog.id,
                            "title": dialog.title or dialog.name or "未知聊天",
                            "type": _get_chat_type(dialog.entity),
                            "username": getattr(dialog.entity, 'username', None),
                            "member_count": getattr(dialog.entity, 'participants_count', None),
                            "description": getattr(dialog.entity, 'about', None),
                            "is_verified": getattr(dialog.entity, 'verified', False),
                            "is_scam": getattr(dialog.entity, 'scam', False),
                            "is_fake": getattr(dialog.entity, 'fake', False),
                            "unread_count": dialog.unread_count,
                            "is_pinned": dialog.pinned,
                            "last_message_date": dialog.date.isoformat() if dialog.date else None,
                            "avatar_url": None  # 快速响应，不下载头像
                        }

                        chats.append(chat_info)
                        chat_count += 1

                    except Exception as e:
                        logger.warning(f"⚠️ 处理对话失败: {e}")
                        continue

                # 断开客户端连接
                await client.disconnect()

                logger.info(f"✅ 成功获取 {chat_count} 个真实聊天")

                # 保存到数据库
                save_result = await TelegramChatService.save_chats(session_id, chats, db)

                if save_result.get("success"):
                    logger.info(f"💾 真实数据已保存到数据库: 新增 {save_result.get('new_count', 0)} 个聊天")

                return {
                    "success": True,
                    "data": {
                        "chats": chats,
                        "count": chat_count,
                        "cached": False,
                        "is_demo": False,
                        "experience_level": "authenticated",
                        "session_id": session_id,
                        "sync_info": {
                            "new_chats": save_result.get('new_count', 0) if save_result.get("success") else 0,
                            "updated_chats": save_result.get('updated_count', 0) if save_result.get("success") else 0
                        }
                    }
                }

            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as telegram_error:
                logger.error(f"❌ Telegram数据获取失败: {telegram_error}")
                raise HTTPException(
                    status_code=500,
                    detail=f"获取聊天列表时发生错误: {str(telegram_error)}"
                )

    except Exception as e:
        logger.error(f"通过session获取聊天列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取聊天列表失败: {str(e)}")


# 健康检查端点
@router.get("/health")
async def health_check():
    """
    健康检查端点
    """
    return {
        "success": True,
        "message": "Telegram Updates API运行正常",
        "timestamp": datetime.now().isoformat()
    }


async def sync_chats_in_background(session_id: str, db):
    """
    后台同步聊天数据（包括下载头像）
    """
    try:
        logger.info(f"🔄 开始后台同步聊天数据: {session_id[:20]}...")

        from telethon import TelegramClient
        from telethon.sessions import StringSession
        from ...services.telegram_chat_service import TelegramChatService

        # 使用session字符串创建客户端
        client = TelegramClient(StringSession(session_id), 0, "")
        await client.connect()

        # 检查是否已授权
        if not await client.is_user_authorized():
            await client.disconnect()
            logger.warning("⚠️ 后台同步失败：Session未授权")
            return

        # 获取聊天列表（包含头像）
        chats = []
        limit = 100

        async for dialog in client.iter_dialogs(limit=limit):
            try:
                # 提取聊天信息（包含头像下载）
                chat_info = {
                    "id": dialog.id,
                    "title": dialog.title or dialog.name or "未知聊天",
                    "type": _get_chat_type(dialog.entity),
                    "username": getattr(dialog.entity, 'username', None),
                    "member_count": getattr(dialog.entity, 'participants_count', None),
                    "description": getattr(dialog.entity, 'about', None),
                    "is_verified": getattr(dialog.entity, 'verified', False),
                    "is_scam": getattr(dialog.entity, 'scam', False),
                    "is_fake": getattr(dialog.entity, 'fake', False),
                    "unread_count": dialog.unread_count,
                    "is_pinned": dialog.pinned,
                    "last_message_date": dialog.date.isoformat() if dialog.date else None,
                    "avatar_url": None
                }

                # 在后台下载头像
                try:
                    from ...services.avatar_manager import AvatarManager
                    avatar_manager = AvatarManager()

                    if chat_info["type"] in ["private", "bot"]:
                        # 用户头像
                        avatar_url = await avatar_manager.get_user_avatar(client, dialog.entity)
                        if avatar_url:
                            chat_info["avatar_url"] = avatar_url
                    else:
                        # 群组/频道头像
                        avatar_url = await avatar_manager.get_chat_avatar(client, dialog.entity)
                        if avatar_url:
                            chat_info["avatar_url"] = avatar_url
                except Exception as avatar_error:
                    logger.warning(f"⚠️ 后台下载头像失败: {avatar_error}")

                chats.append(chat_info)

            except Exception as e:
                logger.warning(f"⚠️ 后台处理对话失败: {e}")
                continue

        # 断开客户端连接
        await client.disconnect()

        # 保存到数据库
        save_result = await TelegramChatService.save_chats(session_id, chats, db)

        if save_result.get("success"):
            logger.info(f"✅ 后台同步完成: 更新 {save_result.get('updated_count', 0)} 个聊天")

    except Exception as e:
        logger.error(f"❌ 后台同步失败: {e}")


async def get_demo_chats():
    """
    获取演示聊天列表
    """
    return {
        "success": True,
        "data": {
            "chats": DEMO_CHATS,
            "count": len(DEMO_CHATS),
            "cached": False,
            "is_demo": True,
            "timestamp": datetime.now().isoformat()
        }
    }