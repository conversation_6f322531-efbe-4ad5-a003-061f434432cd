"""
API v1 module
"""
from fastapi import APIRouter

# 导入真实的Telegram API路由
try:
    from ...api import routes_telegram
except ImportError:
    routes_telegram = None

from . import demo_chats, telegram_updates
# 导入头像路由
try:
    from ...api import routes_avatar
except ImportError:
    routes_avatar = None

api_router = APIRouter()

# Include sub-routers
# 添加真实的Telegram API路由
if routes_telegram:
    api_router.include_router(
        routes_telegram.router,
        tags=["telegram"]
    )

# 真实的telegram-updates API（包含聊天列表等功能）
api_router.include_router(
    telegram_updates.router,
    prefix="/telegram-updates",
    tags=["telegram-updates"]
)

# 演示聊天API（仅演示功能）
api_router.include_router(
    demo_chats.router,
    prefix="/telegram-updates",
    tags=["telegram-updates", "demo"]
)

# TODO: 添加其他路由模块
# api_router.include_router(
#     search.router,
#     prefix="/search",
#     tags=["search"]
# )

# api_router.include_router(
#     analysis.router,
#     prefix="/analysis",
#     tags=["analysis"]
# )

# api_router.include_router(
#     auth.router,
#     prefix="/auth",
#     tags=["auth"]
# )

# api_router.include_router(
#     ai.router,
#     prefix="/ai",
#     tags=["ai"]
# )

# api_router.include_router(
#     sync.router,
#     prefix="/sync",
#     tags=["sync"]
# )

# api_router.include_router(
#     websocket.router,
#     prefix="/ws",
#     tags=["websocket"]
# )

# 添加头像路由
if routes_avatar:
    api_router.include_router(
        routes_avatar.router,
        tags=["avatars"]
    )