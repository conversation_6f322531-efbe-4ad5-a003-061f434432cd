"""
简化的聊天列表API - 快速响应，不下载头像
"""

from fastapi import APIRouter, Query, Depends
from typing import Optional
import logging
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

# 创建路由
router = APIRouter()
logger = logging.getLogger(__name__)

# 导入数据库依赖
from ...core.database import get_db_session
from ...services.telegram_chat_service import TelegramChatService

# 演示数据
DEMO_CHATS = [
    {
        "id": 1001,
        "title": "LINUX DO Channel",
        "type": "channel",
        "username": "linuxdo",
        "participants_count": 5000,
        "description": "Linux技术交流频道",
        "is_verified": True,
        "is_scam": False,
        "is_fake": False,
        "unread_count": 5,
        "is_pinned": True,
        "last_message_date": "2024-01-15T10:30:00Z",
        "avatar_url": None
    },
    {
        "id": 1002,
        "title": "科技圈茶馆",
        "type": "supergroup",
        "username": "tech_chat",
        "participants_count": 1200,
        "description": "科技爱好者交流群",
        "is_verified": False,
        "is_scam": False,
        "is_fake": False,
        "unread_count": 12,
        "is_pinned": False,
        "last_message_date": "2024-01-15T09:45:00Z",
        "avatar_url": None
    },
    {
        "id": 1003,
        "title": "张三",
        "type": "private",
        "username": "zhangsan",
        "participants_count": None,
        "description": None,
        "is_verified": False,
        "is_scam": False,
        "is_fake": False,
        "unread_count": 2,
        "is_pinned": False,
        "last_message_date": "2024-01-15T08:20:00Z",
        "avatar_url": None
    },
    {
        "id": 1004,
        "title": "ChatGPT Bot",
        "type": "bot",
        "username": "chatgpt_bot",
        "participants_count": None,
        "description": "AI聊天机器人",
        "is_verified": True,
        "is_scam": False,
        "is_fake": False,
        "unread_count": 0,
        "is_pinned": False,
        "last_message_date": "2024-01-14T20:15:00Z",
        "avatar_url": None
    }
]

def _get_chat_type(entity):
    """获取聊天类型"""
    from telethon.tl.types import User, Chat, Channel
    
    if isinstance(entity, User):
        if entity.bot:
            return "bot"
        else:
            return "private"
    elif isinstance(entity, Chat):
        return "group"
    elif isinstance(entity, Channel):
        if entity.broadcast:
            return "channel"
        else:
            return "supergroup"
    else:
        return "unknown"


@router.get("/chats")
async def get_chats_simple(
    session_id: Optional[str] = Query(None, description="Telegram session ID"),
    db: AsyncSession = Depends(get_db_session)
):
    """
    快速获取聊天列表 - 不下载头像，优先返回基础数据
    """
    print("================================================================================")
    print("🚨 DEBUG: 获取用户聊天列表")
    print(f"🚨 DEBUG: 请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🚨 DEBUG: session_id参数: {session_id}")
    print("================================================================================")
    try:
        if not session_id:
            # 没有session_id，返回演示数据
            logger.info("没有提供session_id，返回演示数据")
            return {
                "success": True,
                "data": {
                    "chats": DEMO_CHATS,
                    "count": len(DEMO_CHATS),
                    "cached": False,
                    "is_demo": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

        logger.info(f"🚀 快速获取聊天列表: {session_id[:20]}...")

        try:
            from telethon import TelegramClient
            from telethon.sessions import StringSession

            # 使用session字符串创建客户端
            client = TelegramClient(StringSession(session_id), 0, "")
            await client.connect()

            # 检查是否已授权
            if not await client.is_user_authorized():
                await client.disconnect()
                logger.warning("⚠️ Session未授权，返回演示数据")
                return {
                    "success": True,
                    "data": {
                        "chats": DEMO_CHATS,
                        "count": len(DEMO_CHATS),
                        "cached": False,
                        "is_demo": True,
                        "timestamp": datetime.now().isoformat()
                    }
                }

            # 获取聊天列表（不下载头像）
            chats = []
            limit = 50  # 减少数量，快速响应

            async for dialog in client.iter_dialogs(limit=limit):
                try:
                    # 提取聊天信息（不下载头像，快速响应）
                    chat_info = {
                        "id": dialog.id,
                        "title": dialog.title or dialog.name or "未知聊天",
                        "type": _get_chat_type(dialog.entity),
                        "username": getattr(dialog.entity, 'username', None),
                        "participants_count": getattr(dialog.entity, 'participants_count', None),
                        "description": getattr(dialog.entity, 'about', None),
                        "is_verified": getattr(dialog.entity, 'verified', False),
                        "is_scam": getattr(dialog.entity, 'scam', False),
                        "is_fake": getattr(dialog.entity, 'fake', False),
                        "unread_count": dialog.unread_count,
                        "is_pinned": dialog.pinned,
                        "last_message_date": dialog.date.isoformat() if dialog.date else None,
                        "avatar_url": None  # 快速响应，不下载头像
                    }

                    chats.append(chat_info)

                except Exception as e:
                    logger.warning(f"⚠️ 处理对话失败: {e}")
                    continue

            # 断开客户端连接
            await client.disconnect()

            logger.info(f"✅ 快速获取到 {len(chats)} 个聊天")

            # 保存聊天数据到数据库
            logger.info(f"🔄 开始保存 {len(chats)} 个聊天到数据库...")
            try:
                save_result = await TelegramChatService.save_chats(session_id, chats, db)
                if save_result["success"]:
                    logger.info(f"💾 真实数据已保存到数据库: 新增 {save_result['new_count']} 个，更新 {save_result['updated_count']} 个")
                else:
                    logger.error(f"❌ 保存数据库失败: {save_result['error']}")
            except Exception as e:
                logger.error(f"❌ 保存数据库时发生异常: {e}")
                save_result = {
                    "success": False,
                    "error": str(e),
                    "new_count": 0,
                    "updated_count": 0
                }

            return {
                "success": True,
                "data": {
                    "chats": chats,
                    "count": len(chats),
                    "cached": False,
                    "is_demo": False,
                    "experience_level": "authenticated",
                    "session_id": session_id,
                    "sync_info": {
                        "total_chats": len(chats),
                        "timestamp": datetime.now().isoformat(),
                        "database_save": save_result
                    }
                }
            }

        except Exception as e:
            logger.error(f"❌ Telegram API调用失败: {e}")
            # 出错时返回演示数据
            return {
                "success": True,
                "data": {
                    "chats": DEMO_CHATS,
                    "count": len(DEMO_CHATS),
                    "cached": False,
                    "is_demo": True,
                    "timestamp": datetime.now().isoformat()
                }
            }

    except Exception as e:
        logger.error(f"❌ 快速获取聊天列表失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": {
                "chats": DEMO_CHATS,
                "count": len(DEMO_CHATS),
                "is_demo": True
            }
        }


@router.get("/custom-groups")
async def get_custom_groups_simple(session_id: Optional[str] = Query(None, description="Telegram session ID")):
    """
    获取用户自定义分组
    """
    try:
        # 演示自定义分组数据
        demo_groups = [
            {
                "id": "custom_1",
                "title": "工作群组",
                "chat_count": 5,
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "custom_2", 
                "title": "朋友圈",
                "chat_count": 8,
                "created_at": "2024-01-02T00:00:00Z"
            }
        ]
        
        return {
            "success": True,
            "data": {
                "groups": demo_groups,
                "count": len(demo_groups),
                "is_demo": True
            }
        }
        
    except Exception as e:
        logger.error(f"获取自定义分组失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": {
                "groups": [],
                "count": 0
            }
        }
