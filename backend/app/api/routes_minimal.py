"""
TeleSeeker API 路由定义 - 最小版本
完全独立，不依赖任何其他模块
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Optional
from pydantic import BaseModel
import hashlib
import time

# 简化的请求/响应模型
class AuthRequest(BaseModel):
    phone_number: Optional[str] = None
    api_id: Optional[int] = None
    api_hash: Optional[str] = None
    phone_code_hash: Optional[str] = None
    code: Optional[str] = None
    password: Optional[str] = None

class AuthResponse(BaseModel):
    access_token: str
    user_id: int
    token_type: str = "bearer"

# 创建路由器
router = APIRouter()

# ==================== 认证相关接口 ====================

@router.post("/auth/request_code", response_model=Dict[str, str])
async def request_code(request: AuthRequest):
    """请求发送短信验证码（模拟实现）"""
    try:
        print(f"模拟发送验证码到: {request.phone_number}")
        # 模拟返回验证码hash
        phone_code_hash = hashlib.md5(f"{request.phone_number}_{time.time()}".encode()).hexdigest()
        return {"phone_code_hash": phone_code_hash}
    except Exception as e:
        print(f"Request code failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"请求验证码失败: {str(e)}"
        )


@router.post("/auth/login_phone", response_model=AuthResponse)
async def login_phone(request: AuthRequest):
    """使用手机验证码登录（模拟实现）"""
    try:
        print(f"模拟验证码登录: {request.phone_number}, code: {request.code}")
        
        # 模拟验证码验证（简单检查）
        if not request.code or len(request.code) != 6:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码格式错误"
            )
        
        # 模拟用户信息
        user_id = int(hashlib.md5(request.phone_number.encode()).hexdigest()[:8], 16)
        
        # 模拟访问令牌
        access_token = hashlib.md5(f"token_{user_id}_{time.time()}".encode()).hexdigest()
        
        return AuthResponse(
            access_token=access_token,
            user_id=user_id
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"Phone login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/auth/login_password", response_model=AuthResponse)
async def login_password(request: AuthRequest):
    """使用两步验证密码登录（模拟实现）"""
    try:
        print(f"模拟密码验证")
        
        # 模拟密码验证
        if not request.password or len(request.password) < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码不能为空"
            )
        
        # 模拟用户信息
        user_id = int(hashlib.md5("mock_user".encode()).hexdigest()[:8], 16)
        
        # 模拟访问令牌
        access_token = hashlib.md5(f"token_{user_id}_{time.time()}".encode()).hexdigest()

        return AuthResponse(
            access_token=access_token,
            user_id=user_id
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"Password login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"密码验证失败: {str(e)}"
        )


@router.get("/auth/me")
async def get_current_user_info():
    """获取当前用户信息（模拟实现）"""
    return {
        "user_id": 12345,
        "telegram_user_id": 67890,
        "phone_number": "+**********",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@router.post("/auth/logout")
async def logout():
    """注销当前用户会话（模拟实现）"""
    return {"message": "Logged out successfully"}


@router.get("/test")
async def test():
    """测试端点"""
    return {"message": "API is working!", "timestamp": time.time()}


@router.get("/health")
async def health():
    """健康检查端点"""
    return {"status": "healthy", "service": "TeleSeeker API", "timestamp": time.time()}
