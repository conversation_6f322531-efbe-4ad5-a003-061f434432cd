"""
TeleSeeker API 路由定义 - 简化版本
"""
from fastapi import APIRouter, HTTPException, status
from typing import Dict
import logging
import hashlib
import time

from ..schemas import AuthRequest, AuthResponse

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# ==================== 认证相关接口 ====================


@router.post("/auth/request_code", response_model=Dict[str, str])
async def request_code(request: AuthRequest):
    """请求发送短信验证码（模拟实现）"""
    try:
        logger.info(f"模拟发送验证码到: {request.phone_number}")
        # 模拟返回验证码hash
        phone_code_hash = hashlib.md5(f"{request.phone_number}_{time.time()}".encode()).hexdigest()
        return {"phone_code_hash": phone_code_hash}
    except Exception as e:
        logger.error(f"Request code failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"请求验证码失败: {str(e)}"
        )


@router.post("/auth/login_phone", response_model=AuthResponse)
async def login_phone(request: AuthRequest):
    """使用手机验证码登录（模拟实现）"""
    try:
        logger.info(f"模拟验证码登录: {request.phone_number}, code: {request.code}")

        # 模拟验证码验证（简单检查）
        if not request.code or len(request.code) != 6:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="验证码格式错误"
            )

        # 模拟用户信息
        user_id = int(hashlib.md5(request.phone_number.encode()).hexdigest()[:8], 16)

        # 模拟访问令牌
        access_token = hashlib.md5(f"token_{user_id}_{time.time()}".encode()).hexdigest()

        return AuthResponse(
            access_token=access_token,
            user_id=user_id
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Phone login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/auth/login_api_id", response_model=AuthResponse)
async def login_api_id(
    request: AuthRequest,
    session: AsyncSession = Depends(get_async_session)
):
    """使用Telegram API ID/Hash登录"""
    try:
        # 使用API ID/Hash登录
        user_info = await telegram_service.login_with_api(
            request.api_id,
            request.api_hash
        )
        
        # 创建或更新用户
        user = await UserCRUD.get_user_by_telegram_id(session, user_info["id"])
        if not user:
            user = await UserCRUD.create_user(
                session,
                telegram_user_id=user_info["id"],
                api_id=str(request.api_id),
                api_hash=request.api_hash,
                session_string=user_info.get("session_string")
            )
        
        # 生成访问令牌
        access_token = await auth_service.create_access_token(user.user_id)
        
        return AuthResponse(
            access_token=access_token,
            user_id=user.user_id
        )
    except Exception as e:
        logger.error(f"API ID login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/auth/login_password", response_model=AuthResponse)
async def login_password(
    request: AuthRequest,
    session: AsyncSession = Depends(get_async_session)
):
    """使用两步验证密码登录（模拟实现）"""
    try:
        logger.info(f"模拟密码验证")

        # 模拟密码验证
        if not request.password or len(request.password) < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码不能为空"
            )

        # 模拟用户信息（这里应该从之前的登录状态中获取）
        import hashlib
        user_id = int(hashlib.md5("mock_user".encode()).hexdigest()[:8], 16)

        # 创建或更新用户
        user = await UserCRUD.get_user_by_telegram_id(session, user_id)
        if not user:
            user = await UserCRUD.create_user(
                session,
                telegram_user_id=user_id,
                session_string="mock_session_string"
            )

        # 生成访问令牌
        access_token = await auth_service.create_access_token(user.user_id)

        return AuthResponse(
            access_token=access_token,
            user_id=user.user_id
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"密码验证失败: {str(e)}"
        )


@router.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(
    current_user_id: int = Depends(get_current_user),
    session: AsyncSession = Depends(get_async_session)
):
    """获取当前用户信息"""
    try:
        user = await UserCRUD.get_user_by_id(session, current_user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        return UserResponse.from_orm(user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user info failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.post("/auth/logout")
async def logout(current_user_id: int = Depends(get_current_user)):
    """注销当前用户会话"""
    try:
        await telegram_service.logout_user(current_user_id)
        return {"message": "Logged out successfully"}
    except Exception as e:
        logger.error(f"Logout failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"注销失败: {str(e)}"
        )


# ==================== 聊天与消息相关接口 ====================

@router.get("/chats", response_model=List[ChatResponse])
async def get_chats(
    current_user_id: int = Depends(get_current_user),
    session: AsyncSession = Depends(get_async_session)
):
    """获取用户已同步的群组和频道列表"""
    try:
        chats = await ChatCRUD.get_user_chats(session, current_user_id)
        return [ChatResponse.from_orm(chat) for chat in chats]
    except Exception as e:
        logger.error(f"Get chats failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天列表失败"
        )


@router.get("/chats/{telegram_chat_id}/messages", response_model=List[MessageResponse])
async def get_chat_messages(
    telegram_chat_id: int,
    limit: int = 50,
    offset: int = 0,
    before_message_id: Optional[int] = None,
    after_message_id: Optional[int] = None,
    current_user_id: int = Depends(get_current_user),
    session: AsyncSession = Depends(get_async_session)
):
    """获取指定群组/频道的聊天记录"""
    try:
        # 验证聊天所有权
        chat = await ChatCRUD.get_chat_by_telegram_id(session, telegram_chat_id)
        if not chat or chat.user_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="聊天不存在或无权访问"
            )
        
        messages = await MessageCRUD.get_chat_messages(
            session, chat.chat_id, limit, offset, before_message_id, after_message_id
        )
        return [MessageResponse.from_orm(message) for message in messages]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get chat messages failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天记录失败"
        )


@router.post("/chats/{telegram_chat_id}/messages", response_model=MessageResponse)
async def send_message(
    telegram_chat_id: int,
    message_data: Dict[str, str],
    current_user_id: int = Depends(get_current_user),
    session: AsyncSession = Depends(get_async_session)
):
    """在指定群组/频道发送消息"""
    try:
        # 验证聊天所有权
        chat = await ChatCRUD.get_chat_by_telegram_id(session, telegram_chat_id)
        if not chat or chat.user_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="聊天不存在或无权访问"
            )
        
        # 发送消息
        sent_message = await telegram_service.send_message(
            current_user_id, telegram_chat_id, message_data["text"]
        )
        
        # 保存到数据库
        message = await MessageCRUD.create_message(
            session,
            chat.chat_id,
            sent_message["id"],
            sent_message["sender_id"],
            sent_message["sender_name"],
            sent_message["text"],
            sent_message["raw_data"],
            sent_message["date"]
        )
        
        return MessageResponse.from_orm(message)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Send message failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送消息失败"
        )
