"""
头像API路由
提供头像上传、下载、管理功能
"""

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import FileResponse
from pathlib import Path
import os
from typing import Optional

router = APIRouter()

# 头像存储根目录
AVATAR_STORAGE_PATH = Path("storage/avatars")

@router.get("/avatars/{entity_type}/{filename}")
async def get_avatar(entity_type: str, filename: str):
    """
    获取头像文件
    
    Args:
        entity_type: 实体类型 (users, chats, channels)
        filename: 文件名
        
    Returns:
        头像文件
    """
    try:
        # 验证实体类型
        if entity_type not in ["users", "chats", "channels"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的实体类型"
            )
        
        # 构建文件路径
        file_path = AVATAR_STORAGE_PATH / entity_type / filename
        
        # 检查文件是否存在
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="头像文件不存在"
            )
        
        # 返回文件
        return FileResponse(
            path=str(file_path),
            media_type="image/jpeg",
            headers={
                "Cache-Control": "public, max-age=86400",  # 缓存1天
                "Content-Disposition": f"inline; filename={filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 获取头像文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取头像文件失败: {str(e)}"
        )

@router.get("/avatars/user/{user_id}")
async def get_user_avatar_info(user_id: int):
    """
    获取用户头像信息
    
    Args:
        user_id: 用户ID
        
    Returns:
        用户头像信息
    """
    try:
        # 查找用户头像文件
        user_avatar_path = AVATAR_STORAGE_PATH / "users"
        avatar_file = None
        
        if user_avatar_path.exists():
            # 查找以用户ID开头的头像文件
            for file in user_avatar_path.glob(f"{user_id}_*.jpg"):
                avatar_file = file
                break
        
        if avatar_file and avatar_file.exists():
            # 返回头像URL
            relative_path = str(avatar_file.relative_to(AVATAR_STORAGE_PATH))
            avatar_url = f"/api/v1/avatars/{relative_path}"
        else:
            # 返回默认头像URL
            avatar_url = f"https://ui-avatars.com/api/?name=User{user_id}&size=128&background=3b82f6&color=ffffff&bold=true"
        
        return {
            "data": {
                "user_id": user_id,
                "avatar_url": avatar_url,
                "has_custom_avatar": avatar_file is not None
            },
            "success": True,
            "message": "获取用户头像信息成功"
        }
        
    except Exception as e:
        print(f"❌ 获取用户头像信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户头像信息失败: {str(e)}"
        )

@router.get("/avatars/chat/{chat_id}")
async def get_chat_avatar_info(chat_id: int):
    """
    获取群组头像信息
    
    Args:
        chat_id: 群组ID
        
    Returns:
        群组头像信息
    """
    try:
        # 查找群组头像文件
        chat_avatar_path = AVATAR_STORAGE_PATH / "chats"
        avatar_file = None
        
        if chat_avatar_path.exists():
            # 查找以群组ID开头的头像文件
            for file in chat_avatar_path.glob(f"{chat_id}_*.jpg"):
                avatar_file = file
                break
        
        if avatar_file and avatar_file.exists():
            # 返回头像URL
            relative_path = str(avatar_file.relative_to(AVATAR_STORAGE_PATH))
            avatar_url = f"/api/v1/avatars/{relative_path}"
        else:
            # 返回默认头像URL
            avatar_url = f"https://ui-avatars.com/api/?name=Chat{chat_id}&size=128&background=10b981&color=ffffff&bold=true"
        
        return {
            "data": {
                "chat_id": chat_id,
                "avatar_url": avatar_url,
                "has_custom_avatar": avatar_file is not None
            },
            "success": True,
            "message": "获取群组头像信息成功"
        }
        
    except Exception as e:
        print(f"❌ 获取群组头像信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取群组头像信息失败: {str(e)}"
        )

@router.get("/avatars/channel/{channel_id}")
async def get_channel_avatar_info(channel_id: int):
    """
    获取频道头像信息
    
    Args:
        channel_id: 频道ID
        
    Returns:
        频道头像信息
    """
    try:
        # 查找频道头像文件
        channel_avatar_path = AVATAR_STORAGE_PATH / "channels"
        avatar_file = None
        
        if channel_avatar_path.exists():
            # 查找以频道ID开头的头像文件
            for file in channel_avatar_path.glob(f"{channel_id}_*.jpg"):
                avatar_file = file
                break
        
        if avatar_file and avatar_file.exists():
            # 返回头像URL
            relative_path = str(avatar_file.relative_to(AVATAR_STORAGE_PATH))
            avatar_url = f"/api/v1/avatars/{relative_path}"
        else:
            # 返回默认头像URL
            avatar_url = f"https://ui-avatars.com/api/?name=Channel{channel_id}&size=128&background=f59e0b&color=ffffff&bold=true"
        
        return {
            "data": {
                "channel_id": channel_id,
                "avatar_url": avatar_url,
                "has_custom_avatar": avatar_file is not None
            },
            "success": True,
            "message": "获取频道头像信息成功"
        }
        
    except Exception as e:
        print(f"❌ 获取频道头像信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取频道头像信息失败: {str(e)}"
        )

@router.delete("/avatars/cleanup")
async def cleanup_old_avatars(days: int = 30):
    """
    清理旧的头像文件
    
    Args:
        days: 保留天数，默认30天
        
    Returns:
        清理结果
    """
    try:
        from app.services.avatar_service import avatar_service
        
        await avatar_service.cleanup_old_avatars(days)
        
        return {
            "data": {
                "days": days,
                "message": f"已清理 {days} 天前的头像文件"
            },
            "success": True,
            "message": "头像清理完成"
        }
        
    except Exception as e:
        print(f"❌ 清理头像失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理头像失败: {str(e)}"
        )
