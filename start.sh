#!/bin/bash

# TeleSeeker 项目启动脚本 - 增强调试版本

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  [$(date '+%H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ [$(date '+%H:%M:%S')] $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  [$(date '+%H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}❌ [$(date '+%H:%M:%S')] $1${NC}"
}

log_debug() {
    echo -e "${PURPLE}� [$(date '+%H:%M:%S')] DEBUG: $1${NC}"
}

log_step() {
    echo -e "${CYAN}🚀 [$(date '+%H:%M:%S')] STEP: $1${NC}"
}

# 检查当前目录
check_project_structure() {
    log_step "检查项目结构..."

    log_debug "当前工作目录: $(pwd)"
    log_debug "目录内容:"
    ls -la | while read line; do
        log_debug "  $line"
    done

    # 检查关键目录
    if [ ! -d "backend" ]; then
        log_error "backend 目录不存在"
        exit 1
    fi
    log_success "backend 目录存在"

    if [ ! -d "frontend" ]; then
        log_error "frontend 目录不存在"
        exit 1
    fi
    log_success "frontend 目录存在"
}

# 检查虚拟环境
check_virtual_environment() {
    log_step "检查Python虚拟环境..."

    if [ ! -d "venv_new" ]; then
        log_error "虚拟环境 venv_new 不存在"
        log_info "请先运行 python3 -m venv venv_new 创建虚拟环境"
        exit 1
    fi
    log_success "虚拟环境 venv_new 存在"

    # 检查虚拟环境内容
    log_debug "虚拟环境内容:"
    ls -la venv_new/ | while read line; do
        log_debug "  $line"
    done

    # 检查激活脚本
    if [ ! -f "venv_new/bin/activate" ]; then
        log_error "虚拟环境激活脚本不存在"
        exit 1
    fi
    log_success "虚拟环境激活脚本存在"
}

# 检查后端依赖
check_backend_dependencies() {
    log_step "检查后端依赖..."

    log_debug "进入backend目录..."
    cd backend
    log_debug "当前目录: $(pwd)"

    log_debug "激活虚拟环境..."
    source ../venv_new/bin/activate
    log_success "虚拟环境已激活"

    log_debug "检查Python版本..."
    PYTHON_VERSION=$(python --version 2>&1)
    log_success "Python版本: $PYTHON_VERSION"

    log_debug "检查pip版本..."
    PIP_VERSION=$(pip --version 2>&1)
    log_success "pip版本: $PIP_VERSION"

    # 检查关键文件
    if [ ! -f "main.py" ]; then
        log_error "backend/main.py 不存在"
        exit 1
    fi
    log_success "backend/main.py 存在"

    if [ ! -f "requirements.txt" ]; then
        log_warning "backend/requirements.txt 不存在"
    else
        log_success "backend/requirements.txt 存在"
        log_debug "requirements.txt 内容:"
        cat requirements.txt | while read line; do
            log_debug "  $line"
        done
    fi

    cd ..
}

# 检查前端依赖
check_frontend_dependencies() {
    log_step "检查前端依赖..."

    log_debug "进入frontend目录..."
    cd frontend
    log_debug "当前目录: $(pwd)"

    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    NODE_VERSION=$(node --version)
    log_success "Node.js版本: $NODE_VERSION"

    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    NPM_VERSION=$(npm --version)
    log_success "npm版本: $NPM_VERSION"

    # 检查关键文件
    if [ ! -f "package.json" ]; then
        log_error "frontend/package.json 不存在"
        exit 1
    fi
    log_success "frontend/package.json 存在"

    # 检查node_modules
    if [ ! -d "node_modules" ]; then
        log_warning "node_modules 不存在，需要运行 npm install"
        log_info "正在安装前端依赖..."
        npm install
        log_success "前端依赖安装完成"
    else
        log_success "node_modules 存在"
    fi

    cd ..
}

# 启动后端服务
start_backend() {
    log_step "启动后端服务..."

    log_debug "进入backend目录..."
    cd backend

    log_debug "激活虚拟环境..."
    source ../venv_new/bin/activate

    log_debug "检查数据库连接..."
    # 这里可以添加数据库连接检查

    log_info "启动后端服务器..."
    python main.py &
    BACKEND_PID=$!
    log_success "后端服务已启动，PID: $BACKEND_PID"

    # 等待后端启动
    log_debug "等待后端服务启动..."
    sleep 5

    # 检查后端是否正常运行
    log_debug "检查后端服务状态..."
    if kill -0 $BACKEND_PID 2>/dev/null; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        exit 1
    fi

    cd ..
}

# 启动前端服务
start_frontend() {
    log_step "启动前端服务..."

    log_debug "进入frontend目录..."
    cd frontend

    log_info "启动前端开发服务器..."
    npm run dev &
    FRONTEND_PID=$!
    log_success "前端服务已启动，PID: $FRONTEND_PID"

    # 等待前端启动
    log_debug "等待前端服务启动..."
    sleep 3

    # 检查前端是否正常运行
    log_debug "检查前端服务状态..."
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        log_success "前端服务运行正常"
    else
        log_error "前端服务启动失败"
        exit 1
    fi

    cd ..
}

# 清理函数
cleanup() {
    log_warning "正在停止所有服务..."

    if [ ! -z "$BACKEND_PID" ]; then
        log_debug "停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
        log_success "后端服务已停止"
    fi

    if [ ! -z "$FRONTEND_PID" ]; then
        log_debug "停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
        log_success "前端服务已停止"
    fi

    log_info "所有服务已停止"
    exit 0
}

# 主函数
main() {
    log_step "开始启动 TeleSeeker 项目..."

    # 设置信号处理
    trap cleanup INT TERM

    # 执行检查
    check_project_structure
    check_virtual_environment
    check_backend_dependencies
    check_frontend_dependencies

    # 启动服务
    start_backend
    start_frontend

    # 显示启动信息
    echo ""
    log_success "🎉 TeleSeeker 启动完成！"
    log_info "📡 后端API: http://localhost:8000"
    log_info "🎨 前端界面: http://localhost:5173"
    log_info "📚 API文档: http://localhost:8000/api/v1/docs"
    echo ""
    log_info "按 Ctrl+C 停止所有服务"

    # 等待用户中断
    wait
}

# 运行主函数
main "$@"
